"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { Menu, X, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

import { getMenu } from "@/actions/menu";
import Loading from "@/app/loading";

export default function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [services, setServices] = useState<any[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  const menu = [
    { id: 1, title: "Home", link: "/" },
    { id: 2, title: "About", link: "/about" },
    { id: 3, title: "Portfolio", link: "/portfolio" },
    { id: 4, title: "Projects", link: "/projects" },
    { id: 5, title: "Pricing", link: "/pricing" },
    { id: 6, title: "Blog", link: "/blogs" },
  ];

  const menuwithSubmenu = [
    {
      id: 1,
      title: "Company",
      subMenu: [
        { id: 1, title: "How It work", link: "/how-it-work" },
        { id: 3, title: "Contact", link: "/contact" },
      ],
    },
  ];

  const subMenu = [
    { id: 1, title: "How It work", link: "/how-it-work" },
    { id: 3, title: "Contact", link: "/contact" },
  ];

  const fetchServices = async () => {
    const response = await getMenu();
    setServices(response as any);
  };

  useEffect(() => {
    fetchServices();
  }, []);

  return (
    <nav className="fixed backdrop-blur-md bg-black/50 top-0 left-0 right-0 z-50 shadow-md w-full">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Navbar Brand Logo  */}

          <div className="flex items-center">
            <Link href="/" className="cursor-pointer flex-shrink-0">
              <Image
                priority
                src="https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/logo%2Fttfnl.png?alt=media&token=a058c8d9-33db-4686-a0dc-6a4ed4932809"
                alt="thetechfossil, the tech fossil, tech fossil, ttf, wedding website, ecommerce website, e-commerce website,
                online store, online selling website, online product selling, php website, woocommerce, wordpress, online website,
                cheap website, low price website, best price website, lowest price website, web solution, corporate website,
                 designer website, india, delhi website company, website development company in india"
                width={550}
                height={120}
                className="h-8 w-auto"
              />
            </Link>
          </div>

          {/* Desktop Menu */}

          <div className="hidden lg:flex lg:items-center lg:justify-end lg:flex-1 lg:space-x-1">
            {menu &&
              menu.map((item: any) => (
                <Link
                  onClick={() => <Loading />}
                  href={item.link}
                  key={item.id}
                  className="active:cursor-wait cursor-pointer text-lg font-semibold px-3 py-2 rounded-md text-gray-100 hover:text-gray-100 hover:bg-transparent focus:outline-none  dark:text-gray-50"
                >
                  {item.title}
                </Link>
              ))}

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="cursor-pointer inline-flex items-center text-lg font-semibold rounded-md border-none focus:border-none focus:ring-0 active:border-none active:ring-0 text-gray-100 hover:text-gray-100 hover:bg-transparent outline-none focus:outline-none dark:text-gray-50 px-3 py-2"
                >
                  Services <ChevronDown className="ml-1 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-screen max-w-6xl p-4 mr-8 bg-black/70 backdrop-blur-md rounded-md">
                <div className="grid grid-cols-4 gap-4">
                  {services &&
                    services.map((service: any) => (
                      <div key={service.id}>
                        <h2 className="font-bold text-md text-gray-200 dark:text-gray-50 mb-2 ">
                          {service.title}
                        </h2>
                        {service.services.map((item: any) => (
                          <DropdownMenuItem key={item.id} asChild>
                            <Link
                              href={"/services/" + item.slug}
                              className="w-full text-gray-200 cursor-pointer"
                            >
                              {item.title}
                            </Link>
                          </DropdownMenuItem>
                        ))}
                      </div>
                    ))}
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="inline-flex items-center text-lg font-semibold rounded-md border-none focus:border-none focus:ring-0 active:border-none active:ring-0 text-gray-100 hover:text-gray-100 hover:bg-transparent outline-none focus:outline-none dark:text-gray-50 px-3 py-2"
                >
                  Company <ChevronDown className="ml-1 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full p-4 mr-8 bg-black/70 backdrop-blur-md rounded-md">
                <div className="">
                  {menuwithSubmenu &&
                    menuwithSubmenu.map((item: any) => (
                      <div key={item.id}>
                        <h2 className="font-bold text-md text-gray-200 dark:text-gray-50 mb-2">
                          {item.title}
                        </h2>
                        {item.subMenu.map((subItem: any) => (
                          <DropdownMenuItem key={subItem.id} asChild>
                            <Link
                              href={subItem.link}
                              className="w-full text-gray-200"
                            >
                              {subItem.title}
                            </Link>
                          </DropdownMenuItem>
                        ))}
                      </div>
                    ))}
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Mobile menu button */}
          <div className="flex items-center lg:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-gray-100 hover:text-gray-700"
                >
                  <Menu className="h-6 w-6" />
                  <span className="sr-only">Open main menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent
                side="right"
                className="max-w-8xl sm:max-w-9xl overflow-y-auto pb-16"
              >
                <SheetTitle>Menu</SheetTitle>
                <nav className="flex flex-col space-y-4 mt-4">
                  {menu &&
                    menu.map((item: any) => (
                      <Link
                        key={item.id}
                        href={item.link}
                        className="text-base font-medium text-gray-900 dark:text-gray-50"
                      >
                        {item.title}
                      </Link>
                    ))}

                  {subMenu &&
                    subMenu.map((item: any) => (
                      <Link
                        key={item.id}
                        href={item.link}
                        className="text-base font-medium text-gray-900 dark:text-gray-50"
                      >
                        {item.title}
                      </Link>
                    ))}

                  <Accordion type="single" collapsible>
                    <AccordionItem
                      value="services"
                      className="text-base font-medium text-gray-900 dark:text-gray-50"
                    >
                      <AccordionTrigger className="px-4 py-2 text-base font-medium text-gray-100 hover:bg-gray-100 hover:text-gray-900">
                        Services
                      </AccordionTrigger>
                      <AccordionContent>
                        <Accordion type="single" collapsible className="w-full">
                          {services &&
                            services.map((service) => (
                              <AccordionItem
                                value={service.title}
                                key={service.id}
                              >
                                <AccordionTrigger className="px-4 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900">
                                  {service.title}
                                </AccordionTrigger>
                                <AccordionContent>
                                  {service.services.map((item: any) => (
                                    <Link
                                      key={item.id}
                                      href={"/services/" + item.slug}
                                      className="block px-6 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900"
                                      onClick={() => setIsOpen(false)}
                                    >
                                      {item.title}
                                    </Link>
                                  ))}
                                </AccordionContent>
                              </AccordionItem>
                            ))}
                        </Accordion>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </nav>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
}
