"use client";
import React from "react";
``;
import FormModal from "./create-modal";
import CreateForm from "./create-form";
import { Edit } from "lucide-react";
import { updateProject } from "@/actions/project";
import { updateParent } from "@/actions/parent";
import { updateService } from "@/actions/service";
import toast from "react-hot-toast";
import { updatePortfolio } from "@/actions/portfolio";
import { updateSeo } from "@/actions/seo";
import { updateMoreLink } from "@/actions/more";
import { updateCategory } from "@/actions/category";

type Props = {
  data: any;
  fields: any;
  page: string;
};

const EditForm = ({ data, fields, page }: Props) => {
  const handleSubmit = async (formData: Record<string, any>) => {
    if (page === "parent") await updateParent(formData, data?.id);
    if (page === "project") await updateProject(formData, data?.id);
    if (page === "service") await updateService(formData, data?.id);
    if (page === "portfolio") await updatePortfolio(formData, data?.id);
    if (page === "seo") await updateSeo(formData, data?.id);
    if (page === "more") await updateMoreLink(formData, data?.id);
    if (page === "category") await updateCategory(formData);
    if (page === "" || null) toast.error("Please select a page");
  };

  return (
    <>
      <FormModal
        title={`Update ${page}`}
        buttonText={`Update ${page}`}
        icon={<Edit size={20} className="font-bold" />}
      >
        <CreateForm
          isUpdate={true}
          onSubmit={handleSubmit}
          fields={fields as any}
          initialValues={data}
        />
      </FormModal>
    </>
  );
};

export default EditForm;
