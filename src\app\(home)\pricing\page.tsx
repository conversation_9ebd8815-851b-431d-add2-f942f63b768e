"use server";
import React from "react";
import PricingHero from "@/components/main/pricing/Hero";
import PricingMain from "@/components/main/pricing/Main";
import { generateMetadata1 } from "@/lib/utils";

export async function generateMetadata() {
  return generateMetadata1({
    page: "pricing",
  });
}

type Props = {};

const Pricing = async (props: Props) => {
  return (
    <>
      <PricingHero />
      <PricingMain />
    </>
  );
};

export default Pricing;
