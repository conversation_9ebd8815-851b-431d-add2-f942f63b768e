"use server";
import { getProject } from "@/actions/project";
import DetailsPage from "@/components/admin/common/detail-page";
import { projectFields } from "@/lib/staticdata";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};
const ProjectSlugPage = async ({ params }: Props) => {
  const { slug } = await params;
  const project = await getProject(slug);

  return (
    <>
      <DetailsPage data={project} page={"project"} fields={projectFields} />
    </>
  );
};

export default ProjectSlugPage;
