"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface ImageUploadProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange"> {
  multiple?: boolean;
  onChange: (files: File[]) => void;
}

export function ImageUpload({
  multiple,
  onChange,
  ...props
}: ImageUploadProps) {
  const [imageNames, setImageNames] = useState<string[]>([]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files);
      setImageNames(filesArray.map((file) => file.name));
      onChange(filesArray);
    }
  };

  return (
    <div>
      <Input
        type="file"
        accept="image/*"
        onChange={handleImageChange}
        multiple={multiple}
        className="hidden"
        {...props}
      />
      <Button
        type="button"
        variant="outline"
        onClick={() => document.getElementById(props.id || "")?.click()}
      >
        {multiple ? "Upload Images" : "Upload Image"}
      </Button>
      {imageNames.length > 0 && (
        <div className="mt-2">
          {imageNames.map((name, index) => (
            <p key={index} className="text-sm text-muted-foreground">
              {name}
            </p>
          ))}
        </div>
      )}
    </div>
  );
}
