"use server";
import api from "@/lib/api";
import { db } from "@/lib/db";
import { createParentQuery, deleteParentQuery, getParentQuery, getParentsQuery, updateParentQuery } from "@/lib/queries";

export const createParent = async (parent: any) => {
  const data = await createParentQuery(parent);
  return data;
};

export const getParent = async (slug: string) => {
  const data = getParentQuery(slug);
  return data;
};

export const getParents = async () => {
  const parent = await getParentsQuery();
  return parent;
}

export const updateParent = async (parent: any, id: any) => {
  const updatedParent = await updateParentQuery(parent, id)
  return updatedParent;
}

export const deleteParent = async (id: any) => {
  const data = await deleteParentQuery(id)
  return data
}

export const restoreParent = async () => {
  try {
    const oldParents: any = []
    const data = await api.get(`/parent`).then((res) => res.data);
    await data.parent.forEach(async (parent: any) => {
      await oldParents.push({
        slug: parent.slug || "",
        title: parent.title || "",
        description: parent.description || "",
        featureImage: parent.images || "",
        status: 1,
      })
    })

    const data1 = await db.parent.createMany({ data: oldParents });
    return data1

  } catch (err) {
    console.log(err)
    return null;
  }
}