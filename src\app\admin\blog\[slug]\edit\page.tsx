"use server";
import { getBlog } from "@/actions/blog";
import BlogForm from "@/components/admin/blog/BlogForm";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};

const EditBlogPost = async ({ params }: Props) => {
  const { slug } = await params;
  const blog: any = await getBlog(slug);

  return (
    <div className="">
      <BlogForm data={blog && blog} />
    </div>
  );
};

export default EditBlogPost;
