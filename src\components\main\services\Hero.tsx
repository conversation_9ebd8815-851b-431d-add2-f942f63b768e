import React from "react";
import { Highlight } from "../../ui/hero-highlight";

type Props = {
  slug?: string;
  bgImage?: string;
};

const ServiceHero = ({ slug, bgImage }: Props) => {
  return (
    <div
      className="relative h-[90vh]"
      style={{
        backgroundImage: `url(${bgImage})`,
        backgroundPosition: "center center",
        backgroundSize: "cover",
      }}
    >
      <div className="absolute backdrop-blur-sm bg-black/50 z-0 inset-0 flex items-center justify-center text-white font-bold px-4 pointer-events-none text-3xl text-center md:text-4xl lg:text-6xl">
        <p className="bg-clip-text capitalize text-transparent drop-shadow-xl bg-gradient-to-b from-white/80 to-white/50">
          <Highlight className=" text-white">- {slug} -</Highlight>
        </p>
      </div>
    </div>
  );
};

export default ServiceHero;
