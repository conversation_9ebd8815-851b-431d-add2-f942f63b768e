import React from "react";
import ReactMarkdown from "react-markdown";

type Props = {
  description: any;
};

const MarkDown = ({ description }: Props) => {
  return (
    <>
      <ReactMarkdown
        components={{
          h1: ({ children }) => (
            <h1 className="text-5xl font-bold mb-6">{children}</h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-4xl font-bold mb-4">{children}</h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-2xl font-bold mb-4">{children}</h3>
          ),
          h4: ({ children }) => (
            <h4 className="text-2xl font-bold mb-4">{children}</h4>
          ),
          p: ({ children }) => <p className="text-lg mb-8">{children}</p>,
          ul: ({ children }) => (
            <ul className="list-disc list-inside space-y-2">{children}</ul>
          ),
          li: ({ children }) => <li className="ml-4">{children}</li>,
          strong: ({ children }) => <strong>{children}</strong>,
        }}
      >
        {description}
      </ReactMarkdown>
    </>
  );
};

export default MarkDown;
