"use client";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import MDEditor from "@uiw/react-md-editor";
import slugify from "react-slugify";
import { Tag, TagInput } from "emblor";
import toast from "react-hot-toast";
import Image from "next/image";
import { getCategorys } from "@/actions/category";
import { createBlog, updateBlog } from "@/actions/blog";
import { deleteUTFiles } from "@/actions/delete-image";
import { UploadButton } from "../common/upload-button";
import { useRouter } from "next/navigation";

type Props = {
  data?: any;
};

const BlogForm = ({ data }: Props) => {
  const [title, setTitle] = useState(data ? data?.title : "");
  const [slug, setSlug] = useState(data ? data?.slug : "");
  const [category, setCategory] = useState(
    data ? data?.categoryId.toString() : ""
  );
  const [featureImage, setFeatureImage] = useState(
    data ? data?.featureImage : ""
  );
  const [images, setImages] = useState<any[] | null>([]);
  const [status, setStatus] = useState(data ? parseInt(data?.status) : 0);
  const [value, setValue] = useState(
    data ? data?.description : "**Hello world!!!**"
  );
  const [categoryOptions, setCategoryOptions] = useState([]);
  const [tags, setTags] = useState<Tag[]>(data ? data?.tags : []);
  const [activeTagIndex, setActiveTagIndex] = useState<number | null>(null);

  const router = useRouter();

  useEffect(() => {
    setSlug(slugify(title));
  }, [title]);

  const getCategoryOptions = async () => {
    const options: any = await getCategorys();
    setCategoryOptions(options);
  };

  useEffect(() => {
    getCategoryOptions();
  }, []);

  const handleRemoveImage = async () => {
    const url = featureImage;
    if (url) {
      const parts = url.split("/");
      const uniqueCode = parts[parts.length - 1];
      const ab = await deleteUTFiles([uniqueCode]);
      toast.success("Image deleted successfully");
    }
    setFeatureImage("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const newTags = await (tags || []).map((tag: Tag) => ({
      text: tag.text,
    }));

    const blog = {
      title,
      description: value,
      slug,
      tags: newTags,
      featureImage,
      status,
      category: parseInt(category),
    };

    if (data) {
      const updatedBlog = await updateBlog(blog, data?.id);
      if (updatedBlog) {
        toast.success("Blog update successfully");
        router.push("/admin/blog");
      }
    } else {
      const newBlog = await createBlog(blog);
      if (newBlog) {
        toast.success("Blog created successfully");
        router.push("/admin/blog");
      }
    }

    setTitle("");
    setSlug("");
    setTags([]);
    setCategory("");
    setFeatureImage("");
    setImages([]);
    setStatus(0);
    setValue("");
  };

  return (
    <>
      <form onSubmit={handleSubmit}>
        <div className=" flex flex-row ">
          <div className="mr-1 w-full h-screen overflow-y-auto no-scrollbar">
            <>
              <Input
                placeholder="Title"
                className="ring-0 focus:ring-0 focus:outline-none"
                type="text"
                onChange={(e) => setTitle(e.target.value)}
                value={title}
              />
            </>
            <>
              <div className="flex flex-col w-full max-h-[94vh] overflow-hidden">
                <MDEditor
                  height={"1200px"}
                  value={value}
                  textareaProps={{
                    className: "overflow-y-auto max-h-[94vh]",
                    placeholder: "Write your blog here",
                  }}
                  onChange={(val) => setValue(val || "")}
                />
              </div>
            </>
          </div>
          <div className="p-4 border-l-2 border-teal-700">
            <div className="w-full flex justify-end gap-2">
              <Button
                onClick={() => setStatus(0)}
                variant={"outline"}
                className="p-4 mb-2 bg-teal-500 text-white hover:text-teal-500 hover:bg-gray-100 "
                type="submit"
              >
                Save
              </Button>
              <Button
                onClick={() => setStatus(1)}
                className="p-4 mb-2 bg-teal-500 text-white hover:text-teal-500 hover:bg-gray-100 "
                variant={"outline"}
                type="submit"
              >
                Save and Publish
              </Button>
            </div>
            <>
              <Label
                htmlFor="slug"
                className="block my-2 text-sm font-medium text-gray-900 dark:text-gray-400"
              >
                Page Slug
              </Label>
              <Input
                className="cursor-not-allowed opacity-75 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                placeholder="Slug"
                disabled
                value={slug}
                type="text"
              />
            </>
            <>
              <div className="max-w-full mx-auto my-4">
                <Label
                  className="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                  htmlFor="file_input"
                >
                  Upload file
                </Label>
                {!featureImage && <UploadButton setLink={setFeatureImage} />}
                {featureImage && (
                  <Image
                    src={featureImage}
                    alt="featureImage"
                    width={200}
                    height={200}
                    className="w-24 h-24"
                  />
                )}
                {featureImage && (
                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={() => handleRemoveImage}
                      className="text-sm text-red-500"
                    >
                      Remove
                    </button>
                  </div>
                )}
              </div>
            </>

            <>
              <Label htmlFor="tags">Tags</Label>
              <TagInput
                tags={tags}
                setTags={(newTags) => {
                  setTags(newTags);
                }}
                placeholder="Add tags"
                activeTagIndex={activeTagIndex}
                setActiveTagIndex={setActiveTagIndex}
              />
            </>

            <div>
              <Label htmlFor="category">Category</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {categoryOptions.map((item: any) => (
                    <SelectItem key={item.id} value={item.id.toString()}>
                      {item.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </form>
    </>
  );
};

export default BlogForm;
