import Datatable from "@/components/admin/common/data-table";
import React from "react";
import Image from "next/image";
import ViewButtonTable from "@/components/admin/common/view-button-table";
import { getParents } from "@/actions/parent";
import PageHeader from "@/components/admin/common/page-header";
import { parentFields } from "@/lib/staticdata";

type Props = {};

const ParentPage = async (props: Props) => {
  const parents: any = [];
  const datas = await getParents();

  datas.map((item: any) => {
    parents.push({
      title: item.title,
      thumbnail: (
        <Image
          priority
          src={
            item.featureImage
              ? item.featureImage
              : "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/preloader%2Fplaceholder.png?alt=media&token=bf1e9987-d53b-46ad-b92f-c877c3d40982"
          }
          alt={item.title}
          width={100}
          height={100}
        />
      ),
      description: item.description?.substring(0, 150) + "...",
      slug: item.slug,
      status: item.status,
      createdAt: item.createdAt,
      services: item.services?.length,
      action: <ViewButtonTable page={"parent"} slug={item.slug} />,
    });
  });

  return (
    <div className="p-8 flex flex-col justify-center items-center gap-y-8">
      <PageHeader page={"parent"} fields={parentFields} />
      <Datatable data={parents} />
    </div>
  );
};

export default ParentPage;
