"use client";
import React from "react";
import FormModal from "../common/create-modal";
import CreateForm from "./create-form";
import { createProject } from "@/actions/project";
import { createParent } from "@/actions/parent";
import { Plus } from "lucide-react";
import { createService } from "@/actions/service";
import { createPortfolio } from "@/actions/portfolio";
import { createSeo } from "@/actions/seo";
import toast from "react-hot-toast";
import { createMoreLink } from "@/actions/more";
import { createCategory } from "../../../actions/category";

type Props = {
  page: string;
  fields: any;
};

const PageHeader = ({ page, fields }: Props) => {
  const handleSubmit = async (formData: Record<string, any>) => {
    if (page === "parent") await createParent(formData);
    if (page === "project") await createProject(formData);
    if (page === "service") await createService(formData);
    if (page === "portfolio") await createPortfolio(formData);
    if (page === "seo") await createSeo(formData);
    if (page === "more") await createMoreLink(formData);
    if (page === "category") await createCategory(formData);
    if (page === "" || null) toast.error("Please select a page");
  };

  return (
    <div className="w-full bg-neutral-700 flex justify-between p-4 border-2 border-gray-200 rounded-md">
      <h2 className="text-3xl font-bold capitalize">{page}</h2>
      <FormModal
        title={`Create ${page}`}
        buttonText={`Create ${page}`}
        icon={<Plus size={20} className="font-bold" />}
      >
        <CreateForm fields={fields} onSubmit={handleSubmit} />
      </FormModal>
    </div>
  );
};

export default PageHeader;
