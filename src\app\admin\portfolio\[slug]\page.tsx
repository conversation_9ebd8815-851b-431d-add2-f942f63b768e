"use server";
import DetailPage from "@/components/admin/common/detail-page";
import { parentFields, portfolioFields } from "@/lib/staticdata";
import { getPortfolio } from "@/actions/portfolio";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};
const PortfolioSlugPage = async ({ params }: Props) => {
  const { slug } = await params;
  const portfolio = await getPortfolio(slug);

  return (
    <>
      <DetailPage data={portfolio} page="portfolio" fields={portfolioFields} />
    </>
  );
};

export default PortfolioSlugPage;
