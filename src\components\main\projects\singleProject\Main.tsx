"use client";
import React from "react";
import Image from "next/image";
import { convertStatusToValue } from "@/lib/utils";
import { WobbleCard } from "@/components/ui/wobble-card";

type Props = {
  project: any;
};

const ProjectDetailMain = ({ project }: Props) => {
  return (
    <>
      <WobbleCard containerClassName="col-span-1 lg:col-span-3 bg-blue-900 min-h-[500px] lg:min-h-[600px] xl:min-h-[300px]">
        <div className="max-w-3xl">
          <span className="capitalize px-3 py-1.5 text-md shadow-lg shadow-blue-500/40 text-blue-800 bg-blue-100 rounded-full">
            {convertStatusToValue(project.status)}
          </span>
          <h2 className="mt-3 max-w-sm lg:max-w-lg  text-left text-balance text-base md:text-xl lg:text-3xl font-semibold tracking-[-0.015em] text-white">
            {project.title}
          </h2>
          <p className="mt-1 max-w-sm lg:max-w-lg  text-left text-balance text-base md:text-md lg:text-xl font-semibold tracking-[-0.015em] text-white/80">
            {project.type}
          </p>
          <p className="mt-4 max-w-[48rem] text-left  text-base/6 text-neutral-200">
            {project.description}
          </p>
          <p className="flex flex-wrap gap-4 mt-4">
            {project.tags.map((tag: any, index: number) => (
              <span
                key={tag.id}
                className="capitalize px-3 py-1.5 text-md shadow-lg shadow-cyan-500/40 text-cyan-800 bg-cyan-100 rounded-full"
              >
                {tag.text}
              </span>
            ))}
          </p>
        </div>

        {project?.logo && (
          <Image
            priority
            src={project?.logo || "/logo.svg"}
            width={500}
            height={500}
            alt="linear demo image"
            className="absolute -right-1 md:-right-[1%] lg:-right-[1%] -bottom-4 object-contain rounded-2xl"
          />
        )}
      </WobbleCard>
    </>
  );
};

export default ProjectDetailMain;
