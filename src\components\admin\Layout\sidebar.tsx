"use client";
import { useState } from "react";
import Link from "next/link";
import { Sidebar, SidebarBody, SidebarLink } from "@/components/ui/sidebar";

import {
  LayoutDashboard,
  FileText,
  Settings,
  BoxIcon,
  Rss,
  WrenchIcon,
  DollarSign,
  Link2,
  LogOut,
} from "lucide-react";
import Image from "next/image";
import {
  IconBrandGoogle,
  IconCategory,
  IconCategoryPlus,
} from "@tabler/icons-react";
import { motion } from "framer-motion";

const menuItems = [
  {
    icon: (
      <LayoutDashboard className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
    ),
    label: "Dashboard",
    href: "/admin",
  },
  {
    icon: (
      <BoxIcon className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
    ),
    label: "Projects",
    href: "/admin/project",
  },
  {
    icon: (
      <FileText className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
    ),
    label: "Portfolio",
    href: "/admin/portfolio",
  },
  {
    icon: (
      <IconBrandGoogle className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
    ),
    label: "Seo",
    href: "/admin/seo",
  },
  {
    icon: (
      <DollarSign className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
    ),
    label: "Contact",
    href: "/admin/contact",
  },
  {
    icon: (
      <IconCategoryPlus className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
    ),
    label: "Category",
    href: "/admin/category",
  },
  {
    icon: (
      <Rss className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
    ),
    label: "Blog",
    href: "/admin/blog",
  },
  {
    icon: (
      <IconCategory className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
    ),
    label: "Parent",
    href: "/admin/parent",
  },
  {
    icon: (
      <WrenchIcon className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
    ),
    label: "Services",
    href: "/admin/service",
  },
  {
    icon: (
      <Link2 className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
    ),
    label: "More",
    href: "/admin/more",
  },
  {
    icon: (
      <Settings className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
    ),
    label: "Settings",
    href: "/admin/settings",
  },
];

export const AdminSidebar = () => {
  const [open, setOpen] = useState(false);
  return (
    <div>
      <Sidebar open={open} setOpen={setOpen}>
        <SidebarBody className="justify-between gap-10">
          <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
            {open ? <Logo /> : <LogoIcon />}
            <div className="mt-8 flex flex-col gap-2">
              {menuItems.map((link, idx) => (
                <SidebarLink key={idx} link={link} />
              ))}
            </div>
          </div>
          <div>
            <SidebarLink
              link={{
                label: "Logout",
                href: "#",
                icon: (
                  <LogOut className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
                ),
              }}
            />

            <SidebarLink
              link={{
                label: "The Tech Fossil",
                href: "/admin",
                icon: (
                  <Image
                    priority
                    src="/favicon.ico"
                    className="h-7 w-7 flex-shrink-0 rounded-full"
                    width={50}
                    height={50}
                    alt="Avatar"
                  />
                ),
              }}
            />
          </div>
        </SidebarBody>
      </Sidebar>
    </div>
  );
};

export const Logo = () => {
  return (
    <Link
      href="#"
      className="font-normal flex space-x-2 items-center text-sm text-black py-1 relative z-20"
    >
      <div className="h-5 w-6 bg-black dark:bg-white rounded-br-lg rounded-tr-sm rounded-tl-lg rounded-bl-sm flex-shrink-0" />
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="font-medium text-black dark:text-white whitespace-pre"
      >
        The Tech Fossil
      </motion.span>
    </Link>
  );
};
export const LogoIcon = () => {
  return (
    <Link
      href="#"
      className="font-normal flex space-x-2 items-center text-sm text-black py-1 relative z-20"
    >
      <div className="h-5 w-6 bg-black dark:bg-white rounded-br-lg rounded-tr-sm rounded-tl-lg rounded-bl-sm flex-shrink-0" />
    </Link>
  );
};
