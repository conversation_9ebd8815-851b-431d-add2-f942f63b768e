import { getBlogs } from "@/actions/blog";
import Datatable from "@/components/admin/common/data-table";
import React from "react";
import Image from "next/image";
import ViewButtonTable from "@/components/admin/common/view-button-table";
import { Button } from "@/components/ui/button";
import Link from "next/link";

type Props = {};

const BlogPage = async (props: Props) => {
  const blogs: any = [];
  const datas = await getBlogs();

  datas.map((item: any) => {
    blogs.push({
      title: item.title,
      thumbnail: (
        <Image
          priority
          src={
            item.featureImage
              ? item.featureImage
              : "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/preloader%2Fplaceholder.png?alt=media&token=bf1e9987-d53b-46ad-b92f-c877c3d40982"
          }
          alt={item.title}
          width={100}
          height={100}
        />
      ),
      description: item.description.substring(0, 150) + "...",
      slug: item.slug,
      type: item.type,
      status: item.status,
      createdAt: item.createdAt,
      action: <ViewButtonTable page={"blog"} slug={item.slug} />,
    });
  });

  return (
    <div className="p-8 flex flex-col justify-center items-center">
      <div className="p-4 mb-4 max-w-7xl rounded-md w-full flex items-center justify-between border border-neutral-200">
        <h2>Blogs</h2>
        <Button variant="outline" size="lg" asChild>
          <Link href="/admin/blog/new">Create Blog</Link>
        </Button>
      </div>
      <Datatable data={blogs} />
    </div>
  );
};

export default BlogPage;
