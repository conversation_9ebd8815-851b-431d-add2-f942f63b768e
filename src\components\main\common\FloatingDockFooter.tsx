"use client";
import React, { useState } from "react";
import { FloatingDock } from "@/components/ui/floating-dock";
import {
  IconBrandGithub,
  IconBrandX,
  IconExchange,
  IconHome,
  IconNewSection,
  IconTerminal2,
} from "@tabler/icons-react";
import {
  Facebook,
  Twitter,
  Instagram,
  Mail,
  Phone,
  MapPin,
  MessageCircle,
  X,
  Dock,
} from "lucide-react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";

type Props = {};

const FloatingDockFooter = (props: Props) => {
  const [isDockVisible, setIsDockVisible] = useState(false);

  const toggleDock = () => {
    setIsDockVisible(!isDockVisible);
  };
  const links = [
    {
      title: "Home",
      icon: (
        <IconHome className="h-full w-full text-neutral-500 dark:text-neutral-300" />
      ),
      href: "#",
    },

    {
      title: "Products",
      icon: (
        <IconTerminal2 className="h-full w-full text-neutral-500 dark:text-neutral-300" />
      ),
      href: "#",
    },
    {
      title: "Components",
      icon: (
        <IconNewSection className="h-full w-full text-neutral-500 dark:text-neutral-300" />
      ),
      href: "#",
    },
    {
      title: "Aceternity UI",
      icon: (
        <Image
          priority
          src="https://assets.aceternity.com/logo-dark.png"
          width={20}
          height={20}
          alt="Aceternity Logo"
        />
      ),
      href: "#",
    },
    {
      title: "Changelog",
      icon: (
        <IconExchange className="h-full w-full text-neutral-500 dark:text-neutral-300" />
      ),
      href: "#",
    },

    {
      title: "Twitter",
      icon: (
        <IconBrandX className="h-full w-full text-neutral-500 dark:text-neutral-300" />
      ),
      href: "#",
    },
    {
      title: "GitHub",
      icon: (
        <IconBrandGithub className="h-full w-full text-neutral-500 dark:text-neutral-300" />
      ),
      href: "#",
    },
  ];
  return (
    <>
      <AnimatePresence>
        {isDockVisible && (
          <div className="fixed bottom-20 right-4 flex-row flex items-center justify-center h-0 w-full">
            <FloatingDock desktopClassName="" items={links} />
          </div>
        )}
      </AnimatePresence>
      <motion.button
        className="fixed bottom-14 right-4 bg-neutral-900 text-white rounded-full p-3 shadow-lg"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={toggleDock}
        aria-label={isDockVisible ? "Hide dock" : "Show dock"}
      >
        {isDockVisible ? <X size={24} /> : <Dock size={24} />}
      </motion.button>
    </>
  );
};

export default FloatingDockFooter;
