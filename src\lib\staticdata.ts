import { Rocket } from "../components/icons/Rocket";
import { Brush, Laptop, Plane, ShoppingCart, EyeIcon, HeartIcon, Calendar, Code2Icon, Users } from "lucide-react";
import type { Variants } from "motion/react";

const variantsRocket: Variants = {
    animate: {
        x: [0, 0, -3, 2, -2, 1, -1, 0],
        y: [0, -3, 0, -2, -3, -1, -2, 0],
        transition: {
            duration: 6,
            ease: "easeInOut",
            repeat: Infinity,
            repeatType: "reverse" as const,
        },
    },
};

const variantsHeart: Variants = {
    animate: {
        scale: [1, 1.2, 1.4, 1.2, 1],
        transition: {
            duration: 2,
            ease: "easeInOut",
            repeat: Infinity,
            repeatType: "reverse" as const,
        },
    },
};

const variantsEyes: Variants = {
    animate: {
        opacity: [1, 0.5, 0, 0.5, 1],
        transition: {
            duration: 2,
            ease: "easeInOut",
            repeat: Infinity,
            repeatType: "reverse" as const,
        },
    },
};

export const features = [
    {
        Icon: Rocket,
        title: "Mission",
        description:
            "At The Tech Fossil, our unwavering commitment to excellence drives us to exceed client expectations. Guided by a genuine passion, we transform imaginative ideas into captivating, user-centric digital solutions that elevate our clients' online dominance.",
        variants: variantsRocket,
    },
    {
        Icon: EyeIcon,
        title: "Vision",
        description:
            "At The Tech Fossil, our vision blends sophisticated design and uncompromising functionality to create captivating digital experiences. Driven by user-centric principles, we empower clients' customers to effortlessly navigate the digital landscape through innovative solutions.",
        variants: variantsEyes,
    },
    {
        Icon: HeartIcon,
        title: "Passion",
        description:
            "Passionate about our craft, we relentlessly push boundaries to provide our clients with the best and most powerful digital solutions. Through continuous self-challenge, we strive to exceed expectations and deliver exceptional products that propel our clients' success.",
        variants: variantsHeart,
    },
];

export const services1 = [
    {
        id: 1,
        serviceId: "service-01",
        title: "Web Development",
        description:
            "Website development provides functionality to a website. At The Tech Fossil, we understand that a beautiful website also needs powerful functionality to deliver its purpose.",
        icon: Brush,
    },
    {
        id: 2,
        serviceId: "service-02",
        title: "Mobile App Development",
        description:
            "Mobile app development is the process of creating software applications that run on a mobile device, and a typical mobile app utilizes a network connection to work with remote computing resources.",
        icon: Laptop,
    },
    {
        id: 3,
        serviceId: "service-03",
        title: "Digital Marketing",
        description:
            "Digital marketing uses channels like search engines, social media, and email to connect with customers. We help businesses achieve online success through tailored strategies.",
        icon: Plane,
    },
    {
        id: 4,
        serviceId: "service-04",
        title: "E-commerce",
        description:
            "E-commerce provides a seamless way to buy and sell products online. We create solutions that bring convenience to busy lives, ensuring customer satisfaction and accessibility.",
        icon: ShoppingCart,
    },
];

export const servicesName = [
    { id: 1, name: "creativity" },
    { id: 2, name: "planning" },
    { id: 3, name: "design" },
    { id: 4, name: "development" },
];

export const servicesContent = [
    {
        id: 1,
        title: "Get More From Life With Creativity.",
        servicesName: "creativity",
        description:
            "In the ever-evolving digital landscape, creativity has emerged as the most essential factor in web design. Each project requires a unique approach, a distinct look, and a tailored feel to truly captivate your audience. At Tech Fossil, we understand the importance of this principle, and we meticulously craft every web page with this in mind. Our team of design experts carefully considers every aspect of your project, from the layout and aesthetics to the user experience and functionality. By blending technical expertise with artistic vision, we create eye-catching, sophisticated, and elegant web solutions that leave a lasting impression on your visitors. Whether you're launching a new brand, revamping an existing website, or seeking to enhance your online presence, our creative approach ensures that your digital footprint stands out from the crowd. With Tech Fossil, you can unlock the true power of creativity and elevate your web design to new heights.",
        image:
            "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/homepage%2Fservices%2Fcreative.jpg?alt=media&token=4c31c53a-df17-4ea8-a960-e5876961355e",
        alt: "Creative website design",
    },
    {
        id: 2,
        title: "The Home Of Planning.",
        servicesName: "planning",
        description:
            "“Give me six hours to chop down a tree and I will spend the first four sharpening the axe.” ― Abraham Lincoln These wise words from the great Abraham Lincoln perfectly encapsulate the importance of thorough planning in web design. At Tech Fossil, we wholeheartedly embrace this philosophy, understanding that the true foundation for a successful digital project lies in the crucial stage of research and analysis. Just as Lincoln recognized the value of sharpening his axe before tackling the tree, we believe that meticulous planning is the roadmap to creating a truly great product. This stage is where we define Gantt charts to timeline our projects and allocate resources effectively, ensuring that every step is executed with precision. During this crucial phase, we meticulously define the sitemap, plan the layout, refine the UI/UX, and select the right technology stack. It's a process of creating a comprehensive blueprint that will guide us towards completing the project on time and with maximum efficiency. By investing the time and effort upfront, we lay the groundwork for a seamless and successful web design journey. Our clients can rest assured that their digital dreams are in the hands of a team that values thorough preparation and strategic foresight. So, let us sharpen our axes together and embark on a journey of web design excellence, where every step is carefully planned and executed to deliver a truly impressive and impactful result.",
        image:
            "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/homepage%2Fservices%2Fplanning.jpg?alt=media&token=3d4e4d5e-1f0b-4b0a-8a9b-7d2d3b0d1c4c",
        alt: "Web designing planning",
    },
    {
        id: 3,
        title: "Design Your Dream.",
        servicesName: "design",
        description:
            "In a nutshell aWebsite DesignThe Art of Web Design at Tech Fossil In a nutshell, website design is the look and feel of your online presence. At Tech Fossil, we design every single page with meticulous attention to detail and a commitment to elegance, ensuring that your website captivates your audience from the very first glance and truly reflects the unique essence of your business. We understand that your website is a direct representation of your brand, and we work closely with you to determine exactly what you want to convey through this digital canvas. Whether you have a specific color palette or design concept in mind, or you're open to our expert recommendations, we'll collaborate with you to bring your vision to life. Our team of seasoned designers consider themselves artists, and we thrive on the challenge of creating true masterpieces for our clients. We spare no effort in crafting websites that are not only visually stunning but also strategically designed to engage your audience and drive your business forward. At Tech Fossil, we are proud to be recognized as one of the best website design companies in Delhi. Our commitment to excellence, attention to detail, and collaborative approach ensure that your project takes shape seamlessly, allowing you to sit back and watch your digital dreams come to fruition. Ready to elevate your online presence? Get in touch with the web design experts at Tech Fossil and let us help you craft a captivating website that leaves a lasting impression. The tech fossil is Best website designing company in Delhi.",
        image:
            "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/homepage%2Fservices%2Fdesign.jpg?alt=media&token=70bdfdc0-56cc-4c2e-990f-fae24cc0166c",
        alt: "Website design",
    },
    {
        id: 4,
        title: "Development That Matters.",
        servicesName: "development",
        description:
            "Website development is the process of transforming a visually captivating design into a functional and feature-rich digital experience. At Tech Fossil, we consider web development an art form, and we are constantly honing our craft to deliver exceptional results for our clients. Our team of skilled developers works tirelessly to ensure that every website we create is not only aesthetically pleasing but also highly functional and user-friendly. Whether you require a simple static page, a complex web application, an e-commerce platform, or anything in between, we are equipped to bring your digital vision to life. At Tech Fossil, we approach each project with a fresh perspective, constantly expanding our expertise to stay ahead of the curve. From crafting custom content management systems to integrating advanced e-commerce features, our developers have the technical prowess to tackle any challenge that comes their way. But it's not just about the code; it's about the entire user experience. We meticulously design and develop each aspect of your website, ensuring seamless navigation, intuitive workflows, and a delightful interaction for your visitors. Our goal is to create digital experiences that not only captivate but also drive tangible results for your business. Ready to transform your vision into a dynamic, feature-rich website? Reach out to the web development experts at Tech Fossil and let us work our magic. Together, we'll create a digital masterpiece that elevates your online presence and propels your business forward.",
        image:
            "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/homepage%2Fservices%2Fdevelopment.jpg?alt=media&token=9abb82fb-3a9e-45c1-977e-c43af78ad147",
        alt: "Website development",
    },
];

export const stats = [
    {
        id: 1,
        icon: Calendar,
        number: 5,
        title: "Years of Experience",
    },
    {
        id: 2,
        icon: Code2Icon,
        number: 50,
        title: "Projects Done",
    },
    {
        id: 3,
        icon: Users,
        number: 30,
        title: "Happy Clients",
    },
];

export const pricing = [
    {
        id: 1,
        category: "Static Website",
        packages: [
            {
                id: 1,
                title: "Single Page Static Website",
                price: 1999,
                currency: "₹",
                description: "You will get all these awesome services with this great price. Get it now!",
                features: [
                    "Responsive Design",
                    "Complete Setup",
                    "1 year free hosting",
                    "1 year free domain name (if available)",
                    "3 month free maintenance"
                ],
                button: {
                    text: "Get Started",
                    link: "contact"
                }
            },
            {
                id: 2,
                title: "5 Page Static Website",
                price: 4999,
                isBest: true,
                currency: "₹",
                description: "You will get all these awesome services with this great price. Get it now!",
                features: [
                    "Full Code Access",
                    "Responsive Design",
                    "Complete Setup",
                    "1 year free hosting",
                    "1 year free domain name (if available)",
                    "3 month free maintenance"
                ],
                button: {
                    text: "Get Started",
                    link: "contact"
                }
            },
            {
                id: 3,
                title: "7 Page Static Website",
                price: 7999,
                currency: "₹",
                description: "You will get all these awesome services with this great price. Get it now!",
                features: [
                    "Full Code Access",
                    "Responsive Design",
                    "Complete Setup",
                    "1 year free hosting",
                    "1 year free domain name (if available)",
                    "Email Accounts",
                    "3 month free maintenance"
                ],
                button: {
                    text: "Get Started",
                    link: "contact"
                }
            }
        ]
    },
    {
        id: 2,
        category: "Dynamic Website",
        packages: [
            {
                id: 4,
                title: "Dynamic 5 Page Website",
                price: 9999,
                currency: "₹",
                description: "You will get all these awesome services with this great price. Get it now!",
                features: [
                    "Full Code Access",
                    "Responsive Design",
                    "Complete Setup",
                    "Powerful Admin Panel",
                    "1 year free hosting",
                    "1 year free domain name (if available)",
                    "4 month free maintenance"
                ],
                button: {
                    text: "Get Started",
                    link: "contact"
                }
            },
            {
                id: 5,
                title: "10 Page Dynamic Website",
                price: 14999,
                isBest: true,
                currency: "₹",
                description: "You will get all these awesome services with this great price. Get it now!",
                features: [
                    "Full Code Access",
                    "Responsive Design",
                    "Complete Setup",
                    "Powerful Admin Panel",
                    "1 year free hosting",
                    "1 year free domain name (if available)",
                    "4 month free maintenance",
                    "Blog Included"
                ],
                button: {
                    text: "Get Started",
                    link: "contact"
                }
            },
            {
                id: 6,
                title: "Fully Custom Website",
                price: "Enquiry On Call",
                currency: "₹",
                description: "Contact us to customize a solution tailored for you.",
                features: [
                    "Full Code Access",
                    "Responsive Design",
                    "Complete Setup",
                    "Powerful Admin Panel",
                    "1 year free hosting",
                    "1 year free domain name (if available)",
                    "6 months free maintenance",
                    "Blog Included",
                    "Fully Custom Made",
                    "And many more..."
                ],
                button: {
                    text: "Get Started",
                    link: "contact"
                }
            }
        ]
    }
];

export const hiw = [
    {
        title: "Gathering Information",
        content: (
            ` <div>
            <p className= "text-neutral-800 dark:text-neutral-200 text-xs lg:text-sm font-normal mb-8" >
            The Discovery and Research Stage: Laying the Groundwork for Success.
            The initial discovery and research phase is crucial, shaping the
            project's trajectory. By thoroughly understanding client needs and
            expectations through constant communication, we can develop tailored
            solutions that exceed their requirements.This strategic approach
            not only identifies potential challenges early on but also fosters a
            collaborative environment between all stakeholders.Through detailed
            analysis of requirements, market trends, and competitive landscapes,
        we ensure that the project is set up for long - term success.This
            foundational work establishes clarity, direction, and mutual trust,
        paving the way for a seamless and productive project journey.
          </p>
            </div>`
        ),
    },
    {
        title: "Planning",
        content: (
            `   <div>
            <p className= "text-neutral-800 dark:text-neutral-200 text-xs lg:text-sm font-normal mb-8" >
            The Development Stage: Transforming Vision into Reality.After
            discovery and research, the development phase begins.The developer
            and designer collaborate to plan project attributes like design,
        color palette, and technology stack, creating a rough initial
            representation of the final product.This phase involves meticulous
            brainstorming sessions, mockups, and strategy meetings to refine the
    concept.The iterative process ensures that every decision aligns
    with the client's goals and technical feasibility, striking the
            perfect balance between aesthetics and functionality.By the end of
    this stage, a comprehensive roadmap is in place to guide the project
            to its desired outcome.
          </p>
            </div>`
        ),
    },
    {
        title: "Design",
        content: (
            `  <div>
            <p className= "text-neutral-800 dark:text-neutral-200 text-xs lg:text-sm font-normal mb-8" >
            In this phase, the designer leads the creation of the visual and
            interactive elements, enhancing the User Experience(UX) and User
    Interface(UI) to bring the client's vision to life. This process
            involves crafting wireframes, prototypes, and high - fidelity designs
            to capture the look and feel of the product.The designer
            collaborates with the development team to ensure that every element
            is both visually appealing and technically feasible.By gathering
            and incorporating ongoing client feedback, the design evolves into a
    refined, polished concept.This stage emphasizes creating a seamless
            and intuitive user journey, ensuring the final product not only
            meets but exceeds user expectations.
          </p>
        </div>`
        ),
    },
    {
        title: "Collecting Resources",
        content: (
            `<div>
            <p className= "text-neutral-800 dark:text-neutral-200 text-xs lg:text-sm font-normal mb-8" >
            Following the design phase, the development team focuses on creating
            and integrating essential resources to bring the project to life.
            This includes building the database, structuring it based on user
            requirements and existing systems, and sourcing content, images,
        media, and graphics.This phase also involves ensuring data accuracy
            and maintaining data security standards.Every resource is carefully
            curated and optimized to support the project's objectives.
            Collaboration among team members ensures that all components come
            together harmoniously, forming a strong foundation for the upcoming
            development phase.This meticulous effort transforms the conceptual
            design into a fully functional and cohesive solution.
          </p>
        </div>`
        ),
    },
    {
        title: "Development",
        content: (
            `  <div>
            <p className= "text-neutral-800 dark:text-neutral-200 text-xs lg:text-sm font-normal mb-8" >
            After the planning, design, and resource allocation phases, the
            project enters a critical stage where functionality is brought to
    life.Developers meticulously code and integrate each module,
        ensuring that every feature operates seamlessly.This phase also
            involves rigorous collaboration between frontend and backend teams,
        aligning visual elements with technical functionality.Through
            iterative testing and refinement, the team addresses potential
            issues early and optimizes performance.Every aspect of the
            development is geared toward delivering a robust, scalable, and
    user - friendly solution that meets or exceeds client expectations.
          </p>
        </div>`
        ),
    },
    {
        title: "Testing & Deploy",
        content: (
            `     <div>
            <p className= "text-neutral-800 dark:text-neutral-200 text-xs lg:text-sm font-normal mb-8" >
            Testing is a crucial and routine part of the process, ensuring the
            project's integrity and functionality. Every feature, link, and
    module is rigorously tested to identify and address potential
    issues.This includes unit testing, integration testing, and user
            acceptance testing to ensure a seamless experience.The deployment
            phase involves preparing the product for a live environment,
        optimizing performance, and ensuring scalability.Feedback from
            stakeholders is integrated to fine - tune the product before it goes
    live.This stage emphasizes delivering a high - quality, reliable
            solution that is ready for real - world use.
          </p>
        </div>`
        ),
    },
    {
        title: "Maintenance",
        content: (
            ` <div>
            <p className= "text-neutral-800 dark:text-neutral-200 text-xs lg:text-sm font-normal mb-8" >
            It's crucial to recognize that a website is more akin to an ongoing
            service than a static product.Simply delivering a website is not
    enough—the true measure of success lies in its continuous evolution
            and maintenance.Regular updates are required to keep up with
            technological advancements, enhance security, and adapt to user
    needs.This phase also involves monitoring performance, addressing
    bugs, and implementing new features based on user feedback.By
            maintaining a proactive approach, we ensure that the solution
            remains efficient, relevant, and capable of meeting changing
            business goals over time.
          </p>
        </div>`
        ),
    },
];

export const moreLinks = [
    {
        id: 1,
        title: "Wedsite",
        href: "/best-wedding-website-Designing-Company-In-Delhi",
        content: [
            {
                "id": "introduction",
                "heading": "Introduction to Wedding Websites",
                "description": "Wedding websites, also known as wedsites, are a new trend to send invitations to friends and family. They provide a central place for guests to find all the information about your big day. At The Tech Fossil, we create official wedding websites that include all the information your loved ones and far relatives need. These websites include introductions of the bride and groom, beautiful pictures, address, contact information, RSVP details, and more. We listen to our clients, understand their needs, and work hard to deliver customized solutions. We have worked with many wedding planning companies in Delhi, NCR, and directly with couples."
            },
            {
                "id": "life-easier",
                "heading": "Wedding Websites Make Life Easier",
                "description": "A wedding website is an excellent platform for bringing guests together in one virtual place. You can post as much information as needed and set up alerts to notify guests about updates. It simplifies event planning and ensures all guests stay informed."
            },
            {
                "id": "personalized",
                "heading": "We Make It Personal",
                "description": "We personalize the website with your wedding color scheme, photos, and even custom features like a forum for guests to chat. This allows you to start building excitement for your big day early on."
            },
            {
                "id": "secure",
                "heading": "It's Secure",
                "description": "We provide password-protected access to ensure only you can update the information on your website. This helps maintain privacy and control over your wedding details."
            }
        ],
        images: [
            {
                "id": "image-01",
                "url": "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/services%2Fwedding%2F01.jpg?alt=media&token=0a295c6d-4787-432d-ad6d-04bc0b21f154",
                "description": "Image of a wedding website designed by The Tech Fossil"
            },
            {
                "id": "image-02",
                "url": "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/services%2Fwedding%2F03.jpg?alt=media&token=ec0a58c7-26a1-42d8-98b1-f3e689e1d11a",
                "description": "Another example of a wedding website design by The Tech Fossil"
            }
        ]
    },
    {
        id: 2,
        title: "Delhi",
        href: "/Best-Website-Designing-Company-In-Delhi",
        "content": [
            {
                "id": "1",
                "heading": "Introduction to Delhi Website Designing services",
                "description": "We design every page with the effort of achieving perfection. To be honest, the first thing someone notices when they open your website is its design, and then they move to its functionality if they like what they see. A well-designed site encourages visitors to browse more. With this understanding in mind, we design every website and every page with great effort to make your website reach that eye-catching level."
            }
        ],
        "images": [
            {
                "id": "1",
                "url": "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/services%2Fdelhi%2F01.jpg?alt=media&token=88d2c104-b00f-4c6c-af3b-5a4c0492d261",
                "description": "Image representing website designing by The Tech Fossil"
            }
        ]
    },
    {
        id: 3,
        title: "Website Development",
        href: "/website-Designing-development-Company-In-Delhi",
        "content": [
            {
                "id": "1",
                "heading": "Introduction to Website Development",
                "description": "Are you looking for a website design and development company in Delhi? You have come to the right place. With years of experience, we provide the services you need to put your products and services online, helping you reach more customers. Whether you have an established brand or are just starting, we are here for you. Delhi is a city of business and opportunity, and we at The Tech Fossil are proud to be the best website design company in Delhi. We offer quick and reliable web design and development services that are cost-friendly and tailored to your budget and expectations. Our goal is to create the best relationships with our clients, provide technical advice, and help them grow with our support."
            },
            {
                "id": "2",
                "heading": "Our Expertise",
                "description": "We have worked with many industries and completed various projects, including mobile online store websites, news websites, blog websites, portfolio websites, e-commerce websites, multi-vendor e-commerce websites, school websites, college websites, social networking platforms, and more. As a Delhi-based company, we understand the competitive market and the importance of online marketing. Our experience in website design and development allows us to create eye-catching, user-friendly, and secure solutions for our clients. With security features and encrypted admin panels, we ensure your data is protected."
            },
            {
                "id": "3",
                "heading": "Why Choose Us",
                "description": "Our commitment to perfection drives us to focus on every detail. We aim to design products that you and your customers will love, making them easy to use and secure. We value the trust and love of our clients, who have helped us become a leading website design company in Delhi. By choosing us, you become part of The Tech Fossil family. Contact us today to experience our dedication and expertise."
            }
        ],
        "images": [
            {
                "id": "1",
                "url": "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/services%2Fwebsitedesign%2F02.jpg?alt=media&token=d56d2960-15f2-46ee-8644-a88310421238",
                "description": "Image showcasing The Tech Fossil's website designing capabilities"
            },
            {
                "id": "2",
                "url": "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/services%2Fwebsitedesign%2F09.jpg?alt=media&token=cb76bc42-52b1-4caa-bdf2-b8dbd4f125fa",
                "description": "Another example of website design by The Tech Fossil"
            }
        ]
    },
    {
        id: 4,
        title: "East Delhi",
        href: "/website-Design-development-Company-in-east-Delhi",
        "content": [
            {
                "id": "1",
                "heading": "Introduction to East Delhi",
                "description": "Are you looking for a web design and development company in East Delhi? Look no further! With years of experience, we at The Tech Fossil understand customer expectations and work diligently to fulfill all your requirements."
            },
            {
                "id": "2",
                "heading": "Areas We Serve",
                "description": "We provide website design and development services in multiple locations across East Delhi, including:\n\n- Dilshad Garden\n- Gandhi Nagar\n- Geeta Colony\n- Laxmi Nagar\n- Mayur Vihar\n- Nirman Vihar\n- Pandav Nagar\n- Vaishali\n- Yamuna Vihar\n- Shakarpur\n- Shahdara\n- Preet Vihar\n- Patparganj"
            }
        ],
        "images": [
            {
                "id": "1",
                "url": "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/services%2Fdelhi%2F04.jpg?alt=media&token=9571b500-b939-4255-a948-05897fa97dda",
                "description": "Image showcasing The Tech Fossil's services in East Delhi"
            }
        ]
    },
    {
        id: 5,
        title: "West Delhi",
        href: "/website-Design-development-Company-in-west-Delhi",
        "content": [
            {
                "id": "1",
                "heading": "Introduction to West Delhi",
                "description": "We are The Tech Fossil, a web designing and development company in Delhi specializing in creating affordable and effective websites. Contact us via call or WhatsApp to discuss how we can help your business grow."
            },
            {
                "id": "2",
                "heading": "Areas We Serve",
                "description": "We offer web design and development services across West Delhi, including:\n\n- Meera Bagh\n- Najafgarh\n- Indira Gandhi International Airport\n- Ashok Nagar\n- Tilak Nagar\n- Janakpuri\n- Vikaspuri\n- Paschim Vihar\n- Punjabi Bagh\n- Rajouri Garden\n- Patel Nagar\n- Dwarka\n- Hari Nagar\n- Subhash Nagar\n- Uttam Nagar\n- Shadipur Depot\n- Naraina Industrial Area\n- Kirti Nagar\n- Netaji Subhash Place (NSP)\n- Kohat Enclave\n- Rohini\n- Pitampura"
            }
        ],
        "images": [
            {
                "id": "1",
                "url": "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/services%2Fdelhi%2F05.jpg?alt=media&token=f1424a61-2b1f-4083-adee-a5f41033ddeb",
                "description": "Image showcasing The Tech Fossil's services in West Delhi"
            }
        ]
    },
    {
        id: 6,
        title: "North Delhi",
        href: "/website-Design-development-Company-in-north-Delhi",
        "content": [
            {
                "id": "1",
                "heading": "Introduction to North Delhi",
                "description": "We are The Tech Fossil, a web designing and development company in Delhi specializing in affordable and high-quality web development services. Give us a call or leave a message on WhatsApp to discuss your website needs."
            },
            {
                "id": "2",
                "heading": "Areas We Serve",
                "description": "We offer web design and development services across North Delhi, including but not limited to:\n\n- Avantika\n- Burari\n- Chandni Chowk\n- Chawri Bazar\n- Civil Lines\n- Delhi Sadar Bazar\n- Delhi University\n- District Courts\n- Gulabi Bagh\n- Jagatpur\n- Jawahar Nagar\n- Jharoda Majraa\n- Kamla Nagar\n- Malka Ganj\n- Shakti Nagar\n- Timarpur\n- Wazirabad Village\n- Sultan Puri\n- Model Town\n- Narela\n- Karol Bagh\n- Daryaganj\n- Paharganj\n- Connaught Place (CP)"
            }
        ],
        "images": [
            {
                "id": "1",
                "url": "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/services%2Fdelhi%2F06.jpg?alt=media&token=8762f16b-44fe-4003-91bc-6754cc00e712",
                "description": "Image representing The Tech Fossil's web design and development services in North Delhi"
            }
        ]
    },
    {
        id: 7,
        title: "South Delhi",
        href: "/website-Design-development-Company-in-south-Delhi",
        "content": [
            {
                "id": "1",
                "heading": "Introduction to South Delhi",
                "description": "Are you looking for a web design and development company in Delhi? You have come to the right place! With years of experience, we provide all the services needed to take your products and services online, helping you reach more customers. Whether you are an established brand or a startup, we are here to assist you in achieving your goals."
            },
            {
                "id": "2",
                "heading": "Areas We Serve",
                "description": "We proudly offer website design and development services in several prominent locations in South Delhi, including:\n\n- Hauz Khas\n- Greater Kailash\n- Saket\n- Green Park\n- Vasant Kunj\n- Malviya Nagar\n- Kalkaji\n- Lajpat Nagar"
            }
        ],
        "images": [
            {
                "id": "1",
                "url": "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/services%2Fdelhi%2F07.jpg?alt=media&token=037a1f2e-7c23-4309-9b29-e9bc718f376a",
                "description": "Image representing The Tech Fossil's web design and development services in South Delhi"
            }
        ],
    },
    {
        id: 8,
        title: "Noida",
        href: "/website-Design-development-Company-in-noida",
        "content": [
            {
                "id": "1",
                "heading": "Introduction to wedsite designing in Noida",
                "description": "Are you looking for a web design and development company in Noida? With years of experience and expertise, we are here to help you. At The Tech Fossil, we understand your needs and are the perfect fit to take your business online, connecting you to your beloved and future customer base. Contact us, and we’ll always be here to assist."
            },
            {
                "id": "2",
                "heading": "Our Expertise",
                "description": "The Tech Fossil has successfully completed various projects across industries such as Event Management, Apparel, Jewellery & Exports, Art, Associations & NGOs, Aviation, BPO, Clubs and Resorts, Interiors, E-Commerce, News Media, Energy, Exporters, Fashion Designers, Finance, Law & Consultancy, Food & Beverages, Hospitality, Hospitals, Mall Retail, Manufacturing & Industry, Museums & Art Galleries, Online Magazines, Personal Sites, Pharmaceuticals, Photography, Portals, Real Estate & Architects, Recreational Services, Religious Organizations, Restaurants, Schools & Education, Security Agencies, Services, Garments, Trading, Travel & Tourism, and Wedding Services in Noida, Delhi, Ghaziabad, and Greater Noida."
            }
        ],
        "images": [
            {
                "id": "1",
                "url": "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/services%2Fdelhi%2F08.jpg?alt=media&token=48ed57b8-654c-4add-aa3e-8dad03bef95f",
                "description": "Image representing The Tech Fossil's web design and development services in Noida"
            }
        ]
    },
    {
        id: 9,
        title: "Greater Noida",
        href: "/website-Design-development-Company-in-greater-noida",
        "content": [
            {
                "id": "1",
                "heading": "Introduction to website designing in Greater Noida",
                "description": "Are you looking for a web design and development company in Greater Noida? We are here to help you with our expertise in website design and development. At The Tech Fossil, we deliver websites designed and developed using cutting-edge technology. We focus on creating beautiful designs, high-performance features, and user-friendly interfaces that your customers will love and engage with for longer periods."
            },
            {
                "id": "2",
                "heading": "Our Services",
                "description": "We offer a range of services, including Website Design Services, Online Shopping Websites, CMS Websites, Custom Web Application Development, and Wedding Websites. Whether you need a new website built from scratch or want to improve your existing website designs, we can cater to your specific needs."
            },
            {
                "id": "3",
                "heading": "Our Expertise",
                "description": "The Tech Fossil has successfully completed numerous projects across industries such as Event Management, Apparel, Jewellery & Exports, Art, Associations & NGOs, Aviation, BPO, Clubs and Resorts, Interiors, E-Commerce, News Media, Energy, Exporters, Fashion Designers, Finance, Law & Consultancy, Food & Beverages, Hospitality, Hospitals, Mall Retail, Manufacturing & Industry, Museums & Art Galleries, Online Magazines, Personal Sites, Pharmaceuticals, Photography, Portals, Real Estate & Architects, Recreational Services, Religious Organizations, Restaurants, Schools & Education, Security Agencies, Garments, Trading, Travel & Tourism, and Wedding Services in Noida, Delhi, Ghaziabad, and Greater Noida."
            }
        ],
        "images": [
            {
                "id": "1",
                "url": "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/services%2Fdelhi%2F09.jpg?alt=media&token=c1f6e447-869b-451f-ab7d-85e57508c816",
                "description": "Image representing The Tech Fossil's web design and development services in Greater Noida"
            }
        ]
    },
    {
        id: 10,
        title: "Gurgaon",
        href: "/website-Design-development-Company-in-gurgaon",
        "content": [
            {
                "id": "1",
                "heading": "Introduction to website designing in Gurgaon",
                "description": "Gurugram, also known as Gurgaon, is a booming marketplace for many business ventures. We provide the best business web applications and solutions to help you reach the top of the market. Our goal is to let you focus solely on your business while we help you expand your reach. At The Tech Fossil, we grow when our customers grow. With this motto in mind, we work hard for our clients, making us the best website design and development company in Delhi NCR, India."
            },
            {
                "id": "2",
                "heading": "Our Expertise",
                "description": "At The Tech Fossil, we have completed numerous projects across various industries, including Event Management, Apparel, Jewellery & Exports, Art, Associations & NGOs, Aviation, BPO, Clubs and Resorts, Interiors, E-Commerce, News Media, Energy, Exporters, Fashion Designers, Finance, Law & Consultancy, Food & Beverages, Hospitality, Hospitals, Mall Retail, Manufacturing & Industry, Museums & Art Galleries, Online Magazines, Personal Sites, Pharmaceuticals, Photography, Portals, Real Estate & Architects, Recreational Services, Religious Organizations, Restaurants, Schools & Education, Security Agencies, Garments, Trading, Travel & Tourism, and Wedding Services in Noida, Delhi, Ghaziabad, and Greater Noida."
            }
        ],
        "images": [
            {
                "id": "1",
                "url": "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/services%2Fdelhi%2F02.jpg?alt=media&token=6eaadae2-60b9-4a4b-a079-a0767d2be1c9",
                "description": "Image representing The Tech Fossil's website designing and development services in Gurgaon"
            }
        ]

    },
];

export const projectFields = [
    {
        name: "title",
        label: "Title",
        type: "text",
        placeholder: "Enter title",
        autoSlug: true,
    },
    {
        name: "slug",
        label: "Slug",
        type: "text",
        disabled: true,
        placeholder: "Auto-generated slug",
    },

    { name: "tags", label: "Tags", type: "tags", placeholder: "Add tags" },
    { name: "type", label: "Type", type: "text", placeholder: "Enter type" },
    {
        name: "status",
        label: "Status",
        type: "select",
        options: [
            { value: 0, label: "Closed" },
            { value: 1, label: "Upcoming" },
            { value: 2, label: "Live" },
        ],
        placeholder: "Project Status",
    },
    {
        name: "link",
        label: "Link",
        type: "text",
        placeholder: "Enter Project url",
    },
    {
        name: "description",
        label: "Description",
        type: "textarea",
        placeholder: "Enter description",
    },
    { name: "logo", label: "Logo", type: "upload" },
    { name: "featureImage", label: "Feature Image", type: "upload" },
];

export const parentFields = [
    {
        name: "title",
        label: "Title",
        type: "text",
        placeholder: "Enter title",
        autoSlug: true,
    },
    {
        name: "slug",
        label: "Slug",
        type: "text",
        disabled: true,
        placeholder: "Auto-generated slug",
    },
    {
        name: "status",
        label: "Status",
        type: "select",
        options: [
            { value: 0, label: "Deactive" },
            { value: 1, label: "Active" },
        ],
        placeholder: "Parent Status",
    },
    {
        name: "description",
        label: "Description",
        type: "textarea",
        placeholder: "Enter description",
    },
    { name: "featureImage", label: "Feature Image", type: "upload" },
];

export const portfolioFields = [
    {
        name: "title",
        label: "Title",
        type: "text",
        placeholder: "Enter title",
        autoSlug: true,
    },
    {
        name: "slug",
        label: "Slug",
        type: "text",
        disabled: true,
        placeholder: "Auto-generated slug",
    },
    { name: "type", label: "Type", type: "text", placeholder: "Enter type" },
    { name: "tags", label: "Tags", type: "tags", placeholder: "Add tags" },
    {
        name: "status",
        label: "Status",
        type: "select",
        options: [
            { value: 0, label: "Closed" },
            { value: 1, label: "Upcoming" },
            { value: 2, label: "Live" },
        ],
        placeholder: "Project Status",
    },
    {
        name: "link",
        label: "Link",
        type: "text",
        placeholder: "Enter Project url",
    },
    {
        name: "description",
        label: "Description",
        type: "textarea",
        placeholder: "Enter description",
    },
    { name: "logo", label: "Logo", type: "upload" },
    { name: "featureImage", label: "Feature Image", type: "upload" },
];

export const seoFields = [
    {
        name: "type",
        label: "type",
        type: "select",
        options: [
            {
                value: "page",
                label: "Page",
            },
            {
                value: "project-content",
                label: "Project Content",
            },
            {
                value: "service-content",
                label: "Service Content",
            },
            {
                value: "portfolio-content",
                label: "Portfolio Content",
            },
            {
                value: "blog-content",
                label: "Blog Content",
            },
        ],
        placeholder: "Select Seo Type",
    },
    {
        name: "title",
        label: "Title",
        type: "text",
        placeholder: "Enter title",
        autoSlug: true,
    },
    {
        name: "slug",
        label: "Slug",
        type: "text",
        disabled: true,
        placeholder: "Auto-generated Page",
    },
    {
        name: "status",
        label: "Status",
        type: "select",
        options: [
            { value: 0, label: "Deactive" },
            { value: 1, label: "Active" },
        ],
        placeholder: "Parent Status",
    },
    {
        name: "url",
        label: "Url",
        type: "text",
        placeholder: "Enter Page Url",

    },
    {
        name: "keywords",
        label: "Keywords",
        type: "text",
        placeholder: "Enter Page Keywords",
    },
    {
        name: "description",
        label: "Description",
        type: "textarea",
        placeholder: "Enter description",
    },

]

export const moreLinksField = [
    {
        name: "title",
        label: "Title",
        type: "text",
        placeholder: "Enter title",
        autoSlug: true,
    },
    {
        name: "slug",
        label: "Slug",
        type: "text",
        disabled: true,
        placeholder: "Auto-generated slug",
    },
    {
        name: "href",
        label: "Link",
        type: "text",
        placeholder: "Enter link",
    },
    {
        name: "status",
        label: "Status",
        type: "select",
        options: [
            { value: 0, label: "Deactive" },
            { value: 1, label: "Active" },
        ],
        placeholder: "Parent Status",
    },
    { name: "tags", label: "Tags", type: "tags", placeholder: "Add tags" },
]

export const contentField = [
    {
        name: "title",
        label: "Title",
        type: "text",
        placeholder: "Enter title",
        autoSlug: true,
    },
    {
        name: "slug",
        label: "Slug",
        type: "text",
        disabled: true,
        placeholder: "Auto-generated slug",
    },
    {
        name: "status",
        label: "Status",
        type: "select",
        options: [
            { value: 0, label: "Deactive" },
            { value: 1, label: "Active" },
        ],
        placeholder: "Parent Status",
    },
    {
        name: "description",
        label: "Description",
        type: "textarea",
        placeholder: "Enter description",
    },
    { name: "featureImage", label: "Feature Image", type: "upload" },

]

export const categoryField = [
    {
        name: "title",
        label: "Title",
        type: "text",
        placeholder: "Enter title",
        autoSlug: true,
    },
    {
        name: "slug",
        label: "Slug",
        type: "text",
        disabled: true,
        placeholder: "Auto-generated slug",
    },
    {
        name: "status",
        label: "Status",
        type: "select",
        options: [
            { value: 0, label: "Deactive" },
            { value: 1, label: "Active" },
        ],
        placeholder: "Parent Status",
    },
    { name: "featureImage", label: "Feature Image", type: "upload" },
    { name: "tags", label: "Tags", type: "tags", placeholder: "Add tags" },

]