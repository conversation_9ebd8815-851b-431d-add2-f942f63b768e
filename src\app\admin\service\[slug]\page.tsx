"use server";
import { getService } from "@/actions/service";
import DetailsPage from "@/components/admin/common/detail-page";
import { parentOptionFormat } from "@/lib/utils";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};
const ServiceSlugPage = async ({ params }: Props) => {
  const { slug } = await params;
  const service = await getService(slug);

  const servicesFields = [
    {
      name: "title",
      label: "Title",
      type: "text",
      placeholder: "Enter title",
      autoSlug: true,
    },
    {
      name: "slug",
      label: "Slug",
      type: "text",
      disabled: true,
      placeholder: "Auto-generated slug",
    },

    { name: "tags", label: "Tags", type: "tags", placeholder: "Add tags" },
    {
      name: "status",
      label: "Status",
      type: "select",
      options: [
        { value: 0, label: "Deactive" },
        { value: 1, label: "Active" },
      ],
      placeholder: "Project Status",
    },
    {
      name: "parent",
      label: "Parent",
      type: "select",
      options: (await parentOptionFormat()) || [],
      placeholder: "Parent Category",
    },
    {
      name: "description",
      label: "Description",
      type: "textarea",
      placeholder: "Enter description",
    },
    { name: "featureImage", label: "Feature Image", type: "upload" },
  ];

  return (
    <>
      <DetailsPage
        data={
          service
            ? {
                ...service,
                parent: service.parent ? parseInt(service.parent.id as any) : 0,
              }
            : null
        }
        page={"service"}
        fields={servicesFields}
      />
    </>
  );
};

export default ServiceSlugPage;
