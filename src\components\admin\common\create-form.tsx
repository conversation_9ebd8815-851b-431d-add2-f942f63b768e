"use client";
import React, { SetStateAction, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tag, TagInput } from "emblor";
import slugify from "react-slugify";
import { UploadButton } from "../common/upload-button";
import Image from "next/image";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { X } from "lucide-react";
import { deleteUTFiles } from "@/actions/delete-image";
import { cn } from "@/lib/utils";

interface Field {
  name: string;
  label: string;
  type: "text" | "textarea" | "tags" | "upload" | "select";
  placeholder?: string;
  disabled?: boolean;
  autoSlug?: boolean;
  options?: { label: string; value: any }[];
}

interface DynamicFormProps {
  fields: Field[];
  onSubmit: (formData: Record<string, any>) => Promise<void>;
  initialValues?: Record<string, any>;
  isUpdate?: boolean;
}

const CreateForm: React.FC<DynamicFormProps> = ({
  fields,
  onSubmit,
  initialValues = {},
  isUpdate = false,
}) => {
  const [formData, setFormData] = useState<Record<string, any>>(
    initialValues || {}
  );

  initialValues = initialValues
    ? { ...initialValues, status: parseInt(initialValues.status) }
    : {};

  const router = useRouter();
  const [activeTagIndex, setActiveTagIndex] = React.useState<number | null>(
    null
  );

  useEffect(() => {
    if (fields.some((field) => field.autoSlug)) {
      setFormData((prev) => ({
        ...prev,
        slug: slugify(prev.title || ""),
      }));
    }
  }, [formData.title]);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    updateFormData(name, value);
  };

  const updateFormData = (name: string, value: any) => {
    setFormData((prev) => {
      if (prev[name] && prev[name] !== value && name.includes("image")) {
        handleRemoveImage(name);
      }
      return { ...prev, [name]: value };
    });
  };

  const handleRemoveImage = async (fieldName: string) => {
    const url = formData[fieldName];
    if (url) {
      const parts = url.split("/");
      const uniqueCode = parts[parts.length - 1];
      await deleteUTFiles([uniqueCode]);
    }
    setFormData({ ...formData, [fieldName]: "" });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Ensure formData is not null or undefined
    if (!formData || Object.keys(formData).length === 0) {
      toast.error("Form data is empty!");
      return;
    }

    try {
      const newTags = (formData.tags || []).map((tag: Tag) => ({
        text: tag.text,
      }));

      if (fields.some((field) => field.type === "tags")) {
        formData.tags = newTags;
      }

      const finalData = {
        ...formData,
        status: parseInt(formData.status) || 0,
      };

      await onSubmit(finalData);
      toast.success(
        isUpdate ? "Form updated successfully" : "Form submitted successfully"
      );

      if (!isUpdate) {
        setFormData(initialValues);
      }

      router.refresh();
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to submit form.");
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid grid-cols-1  lg:grid-cols-3 gap-4">
        {fields.map((field) => (
          <div
            key={field.name}
            className={cn(
              "space-y-2 mt-2",
              field.type === "textarea" && "col-span-1  lg:col-span-3"
            )}
          >
            <Label htmlFor={field.name}>{field.label}</Label>
            {field.type === "text" && (
              <Input
                id={field.name}
                name={field.name}
                value={formData[field.name] || ""}
                onChange={handleChange}
                type="text"
                placeholder={field.placeholder}
                disabled={field.disabled}
              />
            )}

            {field.type === "select" && field.options && (
              <select
                id={field.name}
                name={field.name}
                value={
                  formData[field.name] !== undefined
                    ? String(formData[field.name])
                    : ""
                }
                onChange={(e) => {
                  const selectedValue = e.target.value;
                  const selectedOption = field.options?.find(
                    (option) => String(option.value) === selectedValue
                  );
                  const convertedValue =
                    selectedOption && !isNaN(Number(selectedOption.value))
                      ? Number(selectedOption.value) // Convert to number if it's a valid number
                      : selectedValue; // Keep as string if it's not a number
                  setFormData({ ...formData, [field.name]: convertedValue });
                }}
                className="w-full bg-neutral-900 text-white p-2 border rounded"
              >
                <option value="">Select an option</option>
                {field.options.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            )}

            {field.type === "textarea" && (
              <Textarea
                id={field.name}
                name={field.name}
                rows={3}
                value={formData[field.name] || ""}
                onChange={handleChange}
                placeholder={field.placeholder}
              />
            )}
            {field.type === "tags" && (
              <TagInput
                tags={formData[field.name] || []}
                setTags={(newTags: SetStateAction<Tag[]>) =>
                  setFormData((prev) => ({
                    ...prev,
                    [field.name]: newTags as any,
                  }))
                }
                placeholder={field.placeholder}
                activeTagIndex={activeTagIndex}
                setActiveTagIndex={setActiveTagIndex}
              />
            )}
            {field.type === "upload" && (
              <div className="relative">
                {!formData[field.name] && (
                  <UploadButton
                    setLink={(url: string) =>
                      setFormData({ ...formData, [field.name]: url })
                    }
                  />
                )}
                {formData[field.name] && (
                  <div className="relative inline-block">
                    <Image
                      src={formData[field.name]}
                      alt={`Uploaded ${field.label}`}
                      width={150}
                      height={150}
                      className="w-auto h-32 object-fit"
                    />
                    <button
                      type="button"
                      onClick={() => handleRemoveImage(field.name)}
                      className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1"
                    >
                      <X size={16} />
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
      <div className="w-full space-y-1 mt-4 flex justify-center items-center">
        <Button type="submit" variant="default" size="lg">
          {isUpdate ? "Update" : "Submit"}
        </Button>
      </div>
    </form>
  );
};

export default CreateForm;
