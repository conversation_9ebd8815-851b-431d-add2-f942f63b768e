"use server";
import React from "react";
import ConactHero from "@/components/main/contact/Hero";
import { generateMetadata1 } from "@/lib/utils";

type Props = {};

export async function generateMetadata() {
  return generateMetadata1({
    page: "contact",
  });
}

const Contact = (props: Props) => {
  return (
    <>
      <ConactHero />
      <section className="my-8 px-16 lg:my-16 lg:px-36">
        <h1 className="text-4xl text-center mb-8"> Contact us </h1>
        <h2 className="text-xl font-bold mt-4 mb-2">
          Get in Touch: Elevate Your Digital Journey with Us
        </h2>
        <p className="capitalize">
          Are you ready to take your digital presence to new heights? Our team
          of experienced designers, developers, and strategists is here to help
          you unlock the full potential of your online success.
        </p>
        <h2 className="text-xl font-bold mt-4 mb-2">Connect with Us</h2>
        <p className="capitalize mb-2">
          Whether you're looking to create a stunning new website, enhance your
          existing digital platform, or explore innovative web solutions, we're
          here to guide you every step of the way. Get in touch with us today to
          discuss your unique needs and objectives.
        </p>
        <ul>
          <li className="mb-1">
            <b>Phone:</b> <span>+************</span>
          </li>
          <li className="mb-1">
            <b>Email:</b> <span>thetechfossil@gmailcom</span>
          </li>
        </ul>
        <h2 className="text-xl font-bold mt-4 mb-2">Schedule a Consultation</h2>
        <p className="capitalize">
          Ready to dive deeper? Schedule a complimentary consultation with our
          team to explore how we can collaborate to transform your digital
          landscape. During our session, we'll discuss your goals, challenges,
          and the customized solutions we can provide to elevate your online
          success.
        </p>
        <a>Schedule a Consultation</a>
        <h2 className="text-xl font-bold mt-4 mb-2">
          Connect with Us on Social Media
        </h2>
        <p className="capitalize mb-1">
          Stay up-to-date with the latest news, insights, and innovative web
          solutions by following us on social media.
        </p>
        <p className="capitalize mb-1">
          <a href="https://twitter.com/thetechfossil" target="_blank">
            Twitter
          </a>{" "}
          |{" "}
          <a href="https://instagram.com/thetechfossil" target="_blank">
            Instagram
          </a>{" "}
          |{" "}
          <a href="https://facebook.com/thetechfossil" target="_blank">
            Facebook
          </a>
        </p>
        <p className="capitalize mb-1">
          We look forward to hearing from you and collaborating to unlock the
          full potential of your digital journey.
        </p>
      </section>
    </>
  );
};

export default Contact;
