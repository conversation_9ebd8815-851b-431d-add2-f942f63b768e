"use server"

import { Resend } from "resend"
import ContactNotify from "@/components/mail/contactnotify"
import { ThankYouEmail } from "@/components/mail/thankyou"

const resend = new Resend(process.env.RESEND_API_KEY)

export async function sendContactEmail(contact: any) {
    try {
        const data = await resend.emails.send({
            from: "<EMAIL>",
            to: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
            subject: "New Contact Form Submission",
            react: ContactNotify({
                data: {
                    name: contact.name,
                    email: contact.email,
                    phone: contact.phone,
                    message: contact.message,
                }

            }),
        })

        const thankYouEmail = await resend.emails.send({
            from: "<EMAIL>",
            to: contact.email,
            subject: "Thank You for Contacting Us",
            react: await ThankYouE<PERSON>({
                name: contact.name,
                email: contact.email,
            }),
        })


        return {
            success: true, data: {
                notification: data,
                thankYou: thankYou<PERSON><PERSON>,
            }
        }
    } catch (error) {
        return { success: false, error }
    }
}
