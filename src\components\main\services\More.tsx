"use client";
import React, { useState } from "react";
import ImagePopup from "@/components/common/image-popup";
import { cn } from "@/lib/utils";
import MarkDown from "@/components/common/mark-down";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";

type Props = {
  data?: any;
};

const MoreLinks = ({ data }: Props) => {
  return (
    <div className="my-16 rounded-md w-full shadow-xl lg:max-w-7xl bg-black bg-opacity-70 backdrop-blur-md container mx-auto p-8">
      <h1 className="text-5xl font-bold mb-6 text-center uppercase">
        {data?.title}
      </h1>

      <div className="grid lg:grid-cols-1 gap-8">
        <div className="space-y-6">
          {data?.contents?.map((item: any) => (
            <section key={item.id}>
              <h2 className="text-3xl font-semibold mb-2">{item.title}</h2>
              <div
                className={cn(
                  "h-full grid gap-4",
                  item.type === "image" ? "grid-cols-1" : "grid-cols-1"
                )}
              >
                <MarkDown description={item.description} />
                {item.type === "image" && (
                  <Image
                    width={100}
                    height={100}
                    className="w-64 h-64 object-contain"
                    src={item.featureImage}
                    alt={item.title}
                  />
                )}
              </div>
            </section>
          ))}
        </div>

        <div className="space-y-8">
          {data?.tags && (
            <>
              <div className="flex flex-wrap gap-4">
                {data?.tags.map((tag: any) => (
                  <Badge
                    key={tag.id}
                    variant="gradient"
                    size="lg"
                    animation="bounce"
                  >
                    {tag.text}
                  </Badge>
                ))}
              </div>
            </>
          )}

          {data?.images && <ImagePopup images={data?.images} />}
        </div>
      </div>
    </div>
  );
};

export default MoreLinks;
