"use server";
import React from "react";
import { BlogGrid } from "@/components/main/blog/BlogGrid";
import { db } from "@/lib/db";
import { generateMetadata1 } from "@/lib/utils";

type Props = {};

export async function generateMetadata() {
  return generateMetadata1({
    page: "blogs",
  });
}

const BlogPage = async (props: Props) => {
  const posts = await db.blog.findMany();

  return (
    <div className="py-32 h-full md:min-h-screen">
      <BlogGrid posts={posts} />
    </div>
  );
};

export default BlogPage;
