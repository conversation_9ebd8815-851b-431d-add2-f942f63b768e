import React from "react";
import MarkDown from "@/components/common/mark-down";

type Props = {
  service: any;
};

const ServiceContent = ({ service }: Props) => {
  return (
    <div className="bg-black/50 backdrop-blur-xl my-16 rounded-3xl max-w-7xl mx-auto px-4 py-8">
      <div className={`w-full px-4`}>
        <div className="web-content capitalize text-gray-700 dark:text-gray-200">
          {service && <MarkDown description={service.description as string} />}
        </div>
      </div>
    </div>
  );
};

export default ServiceContent;
