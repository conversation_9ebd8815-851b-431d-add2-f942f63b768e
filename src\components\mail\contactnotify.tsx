import React from "react";

type Props = {
  data: any;
};

const ContactNotify = ({ data }: Props) => {
  return (
    <div
      style={{
        fontFamily:
          '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
        maxWidth: "600px",
        margin: "0 auto",
        padding: "20px",
        backgroundColor: "#ffffff",
      }}
    >
      <table
        width="100%"
        cellPadding="0"
        cellSpacing="0"
        style={{ backgroundColor: "#f0f4f8", padding: "20px" }}
      >
        <tr>
          <td>
            <h1
              style={{
                color: "#2d3748",
                fontSize: "24px",
                fontWeight: "bold",
                textAlign: "center",
                margin: "0 0 20px",
              }}
            >
              New Contact Form Submission
            </h1>

            <div
              style={{
                backgroundColor: "#ffffff",
                borderRadius: "8px",
                padding: "20px",
                boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
              }}
            >
              <p
                style={{
                  color: "#4a5568",
                  fontSize: "16px",
                  lineHeight: "1.5",
                  margin: "0 0 16px",
                }}
              >
                You have received a new message from the contact form.
              </p>

              <table
                width="100%"
                cellPadding="0"
                cellSpacing="0"
                style={{
                  backgroundColor: "#f7fafc",
                  borderRadius: "4px",
                  marginBottom: "20px",
                }}
              >
                <tr>
                  <td style={{ padding: "10px 20px" }}>
                    <h2
                      style={{
                        color: "#2d3748",
                        fontSize: "18px",
                        fontWeight: "bold",
                        margin: "0 0 10px",
                      }}
                    >
                      Contact Details:
                    </h2>
                    <p
                      style={{
                        color: "#4a5568",
                        fontSize: "14px",
                        margin: "0 0 5px",
                      }}
                    >
                      <strong>Name:</strong> {data.name}
                    </p>
                    <p
                      style={{
                        color: "#4a5568",
                        fontSize: "14px",
                        margin: "0",
                      }}
                    >
                      <strong>Email:</strong> {data.email}
                    </p>
                  </td>
                </tr>
              </table>

              <h3
                style={{
                  color: "#2d3748",
                  fontSize: "16px",
                  fontWeight: "bold",
                  margin: "0 0 10px",
                }}
              >
                Message:
              </h3>
              <p
                style={{
                  color: "#4a5568",
                  fontSize: "14px",
                  lineHeight: "1.5",
                  backgroundColor: "#f7fafc",
                  borderRadius: "4px",
                  padding: "10px",
                  whiteSpace: "pre-wrap",
                }}
              >
                {data.message}
              </p>

              <h3
                style={{
                  color: "#2d3748",
                  fontSize: "16px",
                  fontWeight: "bold",
                  margin: "0 0 10px",
                }}
              >
                Phone:
              </h3>
              <p
                style={{
                  color: "#4a5568",
                  fontSize: "14px",
                  lineHeight: "1.5",
                  backgroundColor: "#f7fafc",
                  borderRadius: "4px",
                  padding: "10px",
                  whiteSpace: "pre-wrap",
                }}
              >
                {data.phone}
              </p>

              <div
                style={{
                  borderTop: "1px solid #e2e8f0",
                  marginTop: "20px",
                  paddingTop: "20px",
                }}
              >
                <p
                  style={{
                    color: "#718096",
                    fontSize: "14px",
                    fontStyle: "italic",
                    margin: "0",
                  }}
                >
                  This is an automated notification. Please do not reply to this
                  email.
                </p>
              </div>
            </div>
          </td>
        </tr>
      </table>
    </div>
  );
};

export default ContactNotify;
