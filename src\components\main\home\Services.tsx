"use client";

import { motion } from "motion/react";
import React from "react";
import { services1 } from "../../../lib/staticdata";

type Props = {};

const HomeServices = (props: Props) => {
  return (
    <section
      style={{
        backgroundImage:
          "url('https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/homepage%2Fservices%2Fservices-right-01.jpg?alt=media&token=382bd761-3329-412f-8f96-a2b99f38391d')",
      }}
      className="bg-cover bg-center bg-fixed bg-no-repeat h-full lg:h-screen"
    >
      <div className="h-full lg:h-screen bg-gray-700/20 ">
        <div className="p-8 lg:py-12">
          <div className="text-center mb-12">
            <h5 className="text-lg font-semibold text-gray-50">What We Do</h5>
            <h2 className="text-3xl font-bold text-gray-100">Our Services</h2>
          </div>
          <div className="h-full flex lg:mt-8 w-full lg:w-11/12 mx-auto">
            <div className="h-full grid grid-row-1 lg:grid-cols-2 gap-4 justify-center items-center">
              {services1.map((service) => (
                <div
                  key={service.id}
                  className="h-full bg-white/80 backdrop-blur-sm rounded-md p-4 flex gap-4"
                >
                  <motion.div
                    animate={{
                      translateY: [40, 130, 40],
                      translateX: [10, 30, 10],
                      scale: [1, 1.5, 1],
                    }}
                    transition={{
                      duration: 16,
                      repeat: Infinity,
                      repeatType: "reverse",
                    }}
                    className="text-center text-indigo-700 text-4xl"
                  >
                    <service.icon size={40} />
                  </motion.div>
                  <div className="p-8 ml-4">
                    <h5 className="text-sm font-semibold text-gray-800">
                      {service.serviceId}
                    </h5>
                    <h4 className="text-xl font-bold text-gray-900">
                      {service.title}
                    </h4>
                    <p className="mt-2 text-gray-900 text-left">
                      {service.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HomeServices;
