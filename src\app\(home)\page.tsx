"use server";
import <PERSON> from "@/components/main/home/<USER>";
import HomeAbout from "@/components/main/home/<USER>";
import HomeStatement from "@/components/main/home/<USER>";
import HomeServices from "@/components/main/home/<USER>";
import HomeServices2 from "@/components/main/home/<USER>";
import HomeStats from "@/components/main/home/<USER>";
import { generateMetadata1 } from "@/lib/utils";

export async function generateMetadata() {
  return generateMetadata1({
    page: "home",
  });
}
function Home() {
  return (
    <div className="w-screen lg:w-full overflow-hidden">
      <Hero />
      <HomeAbout />
      <HomeStatement />
      <HomeServices />
      <HomeServices2 />
      <HomeStats />
    </div>
  );
}

export default Home;
