"use server";
import DetailPage from "@/components/admin/common/detail-page";
import { categoryField } from "@/lib/staticdata";
import { getCategory } from "@/actions/category";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};
const CategorySlugPage = async ({ params }: Props) => {
  const { slug } = await params;
  const category = await getCategory(slug);

  return (
    <>
      <DetailPage
        data={category}
        page="category"
        fields={categoryField}
        isContnent={false}
        isImages={false}
      />
    </>
  );
};

export default CategorySlugPage;
