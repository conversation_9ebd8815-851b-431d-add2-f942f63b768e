"use server";
import React from "react";
import Link from "next/link";
import { ExternalLink } from "lucide-react";
import ProjectDetailHero from "@/components/main/projects/singleProject/Hero";
import ProjectDetailMain from "@/components/main/projects/singleProject/Main";
import { generateMetadata1 } from "@/lib/utils";
import { getProject } from "@/actions/project";
import NotFound from "@/app/not-found";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};

export async function generateMetadata({ params }: Props) {
  const { slug } = await params;
  return generateMetadata1({
    page: slug,
  });
}

const SingeProject = async ({ params }: Props) => {
  const { slug } = await params;
  const project: any = await getProject(slug);

  if (!project) {
    return <NotFound />;
  }

  return (
    <>
      <div className="p-10 flex flex-col overflow-hidden">
        <ProjectDetailHero project={project} />
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 max-w-7xl mx-auto w-full">
          <ProjectDetailMain project={project} />
          <div className="fixed bottom-4 right-4 z-50 inline-flex items-center justify-center w-48 h-48">
            {project.link && (
              <Link
                href={project.link}
                target="_blank"
                className="mt-4 w-full h-12 flex shadow-2xl justify-center items-center bg-trasparent text-white dark:bg-white dark:text-black text-sm px-2 py-1 rounded-md border border-black"
              >
                Visit Website
                <ExternalLink size={16} strokeWidth={3} className="ml-4" />
              </Link>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default SingeProject;
