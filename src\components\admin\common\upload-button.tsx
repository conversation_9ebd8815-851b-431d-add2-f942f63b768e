"use client";

import { useState } from "react";
import { useUploadThing } from "@/lib/uploadthing";
import { Button } from "@/components/ui/button";
import { UploadCloud, CheckCircle, AlertCircle } from "lucide-react";
import toast from "react-hot-toast";

type Props = {
  setLink: any;
};

export function UploadButton({ setLink }: Props) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadComplete, setUploadComplete] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const { startUpload } = useUploadThing("imageUploader");

  const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    const file = e.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    setUploadError(null);

    try {
      const res = await startUpload([file]);
      if (res && res[0].url) {
        setUploadComplete(true);
        setLink(res[0].url);
      } else {
        throw new Error("Upload failed");
      }
    } catch (error) {
      console.error("Upload error:", error);
      setUploadError("Upload failed. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      <Button
        size={"lg"}
        variant={"default"}
        type="button"
        className={`w-64 h-16 ${
          isUploading ? "opacity-50 cursor-not-allowed" : ""
        }`}
        disabled={isUploading}
      >
        <label className="cursor-pointer flex items-center justify-center w-full h-full">
          <input
            type="file"
            className="hidden"
            onChange={handleUpload}
            accept="image/*"
            disabled={isUploading}
          />
          {isUploading ? (
            <span className="animate-pulse">Uploading...</span>
          ) : uploadComplete ? (
            <span className="flex items-center">
              <CheckCircle className="mr-2" /> Upload Complete
            </span>
          ) : (
            <span className="flex items-center">
              <UploadCloud className="mr-2" /> Upload Image
            </span>
          )}
        </label>
      </Button>
      {uploadError && toast.error(uploadError)}
    </div>
  );
}
