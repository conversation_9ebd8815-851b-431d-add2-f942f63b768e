"use server";
import api from "@/lib/api";
import { db } from "@/lib/db";
import { generateSEOWithOllama } from "@/lib/ollama";
import { generateSEOContent } from "@/lib/openai";
import { createSeoQuery, deleteSeoQuery, getSeoQuery, getSingleSeoQuery, updateSeoQuery } from "@/lib/queries";

export const createSeo = async (seo: any) => {
    const data = await createSeoQuery(seo);
    return data
}

export const getSeos = async () => {
    const data = await getSeoQuery();
    return data
}

export const getSeo = async (slug: string) => {
    const data = await getSingleSeoQuery(slug)
    return data
}

export const getPageSeo = async () => {
    const data = await db.seo.findMany({
        where: {
            type: "page",
        },
    });

    return data
}

export const updateSeo = async (seo: any, id: any) => {
    const data = await updateSeoQuery(seo, id);
    return data
}

export const deleteSeo = async (id: any) => {
    const data = await deleteSeoQuery(id);
    return data
}

export async function generateSEO(title: any, content: any, type: string, slug?: any, url?: any) {
    try {
        // Generate SEO metadata using AI
        const data = { title, body: content }
        //const seoResult = await generateSEOContent(data, type);
        const seoResult = await generateSEOWithOllama(data);
        if (!seoResult) {
            throw new Error("SEO result is null");
        }

        const isSeoExist = await getSeo(slug)

        let seo;

        if (!isSeoExist && seoResult) {
            seo = await db.seo.create({ data: { title: seoResult.title, description: seoResult.description, keywords: seoResult.keywords, page: slug, type: type, url: url || "/" + slug, status: 1 } })
        } else {
            if (isSeoExist && seoResult) {
                seo = await updateSeo({ title: seoResult.title, description: seoResult.description, keywords: seoResult.keywords, page: slug, type: type, url: url || "/" + slug, status: 1 }, isSeoExist.id)
            }
        }

        console.log("dekh seo", seoResult, seo);


        // Store SEO metadata in database
        return { success: true, data: seoResult };
    } catch (error) {
        console.error("Error generating SEO metadata:", { success: false, error: (error as Error).message });
        return { success: false, error: (error as Error).message };
    }
}

export const restoreSeo = async () => {
    try {
        const oldSeo: any = []
        const data = await api.get(`/seo`).then((res) => res.data);
        await data.seo.forEach(async (seo: any) => {
            await oldSeo.push({
                type: seo.name || "",
                page: seo.page || "",
                title: seo.title || "",
                description: seo.description || "",
                keywords: seo.keywords || "",
                status: 1,
                url: seo.url || ""
            })
        })
        const data1 = await db.seo.createMany({ data: oldSeo });
        return data1
    } catch (err) {
        console.log(err)
        return null;
    }
}