"use server";
import DetailPage from "@/components/admin/common/detail-page";
import { seoFields } from "@/lib/staticdata";
import { getSeo } from "@/actions/seo";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};
const SeoSlugPage = async ({ params }: Props) => {
  const { slug } = await params;
  const seo = await getSeo(slug);

  return (
    <>
      <DetailPage data={seo} page="seo" fields={seoFields} isImages={false} />
    </>
  );
};

export default SeoSlugPage;
