"use server";
import { db } from "./db"


//common
const getFieldName = async (page: string, id: any) => {
    let something = {};
    if (page === "project") {
        something = {
            projectId: id
        }
    }
    if (page === "service") {
        something = {
            serviceId: id
        }
    }
    if (page === "blog") {
        something = {
            blogId: id
        }
    }
    if (page === "portfolio") {
        something = {
            portfolioId: id
        }
    }
    if (page === "more") {
        something = {
            moreId: id
        }
    }

    return something;
}

const connectKare = async (page: any, id: any) => {
    let something = {};
    if (page === "project") {
        something = {
            project: {
                connect: {
                    id: parseInt(id)
                }
            }
        }
    }
    if (page === "service") {
        something = {
            service: {
                connect: {
                    id: parseInt(id)
                }
            }
        }
    }
    if (page === "blog") {
        something = {
            blog: {
                connect: {
                    id: parseInt(id)
                }
            }
        }
    }
    if (page === "portfolio") {
        something = {
            portfolio: {
                connect: {
                    id: parseInt(id)
                }
            }
        }
    }
    if (page === "more") {
        something = {
            more: {
                connect: {
                    id: parseInt(id)
                }
            }
        }
    }

    return something;
}


const deleteTagsBeforeModify = async (id: any, page: string) => {
    const field = await getFieldName(page, id)

    return await db.tag.deleteMany({
        where: field
    })
}


// Menu
export const getMenuItemsQuery = async () => {
    const data = await db.parent.findMany({
        where: {
            status: 1
        },
        orderBy: {
            id: "desc",
        },
        include: {
            services: true,
        },
    });

    return data
}

// services
export const getServiceQuery = async (slug: string) => {
    const data = await db.service.findUnique({
        where: {
            slug,
        },
        include: {
            images: true,
            parent: true,
            tags: true
        },
    });

    return data;
}

export const getServicesQuery = async () => {
    const data = await db.service.findMany({
        orderBy: {
            createdAt: "desc",
        },
        include: {
            parent: true,
            tags: true,
            images: true
        }
    });

    return data
}

export const createServiceQuery = async (service: any) => {
    const data = await db.service.create({
        data: {
            ...service,
            tags: {
                createMany: {
                    data: service.tags
                }
            },
            parent: {
                connect: {
                    id: parseInt(service.parent)
                }
            }
        }
    });
    return data
}

export const updateServiceQuery = async (service: any, sid: any) => {
    await deleteTagsBeforeModify(sid, "service")
    const { images, id, parentId, ...serviceData } = service
    const data = await db.service.update({
        data: {
            ...serviceData,
            tags: {
                createMany: {
                    data: serviceData.tags
                }
            },
            parent: {
                connect: {
                    id: parseInt(serviceData.parent)
                }
            }
        },
        where: {
            id: sid
        }
    });
    return data
}

export const deleteServiceQuery = async (id: any) => {
    await deleteTagsBeforeModify(id, "service")
    const data = await db.service.delete({
        where: {
            id
        }
    });
    return data
}

//parent
export const createParentQuery = async (parent: any) => {
    const data = await db.parent.create({
        data: {
            ...parent
        }
    });
    return data
}

export const getParentQuery = async (slug: string) => {
    const data = await db.parent.findUnique({
        where: {
            slug,
        },
        include: {
            services: true
        },
    });
    return data
}

export const getParentsQuery = async () => {
    const data = await db.parent.findMany({
        orderBy: {
            createdAt: "desc",
        },
        include: {
            services: true
        }
    });

    return data
}

export const updateParentQuery = async (parent: any, id: any) => {

    const { services, ...parentData } = parent

    const data = await db.parent.update({
        where: { id },
        data: { ...parentData }
    })

    return data
}

export const deleteParentQuery = async (id: any) => {
    const data = await db.parent.delete({
        where: {
            id
        }
    })

    return data
}

//Contact 
export const createContactQuery = async (contact: any) => {
    const data = await db.contact.create({
        data: {
            ...contact
        }
    });
    return data
}

export const getContactsQuery = async () => {
    const data = await db.contact.findMany({
        orderBy: {
            createdAt: "desc",
        },
    });

    return data
}

// Portfolio Query

export const createPortfolioQuery = async (portfolio: any) => {
    const data = await db.portfolio.create({
        data: {
            ...portfolio,
            tags: {
                createMany: {
                    data: portfolio.tags
                }
            }
        }
    });
    return data
}

export const getPortfoliosQuery = async () => {
    const data = await db.portfolio.findMany(
        {
            orderBy: {
                createdAt: "desc",
            },
            include: {
                tags: true,
                images: true,
            }
        }
    );

    return data
}

export const getPortfolioQuery = async (slug: string) => {
    const data = await db.portfolio.findUnique({
        where: {
            slug,
        },
        include: {
            images: true,
        },
    });

    return data;
}

export const updatePortfolioQuery = async (portfolio: any, id: any) => {
    await deleteTagsBeforeModify(id, "portfolio")
    const { images, id: pid, ...portfolioData } = portfolio
    const data = await db.portfolio.update({
        data: {
            ...portfolioData,
            tags: {
                createMany: {
                    data: portfolioData.tags
                }
            }
        },
        where: {
            id: pid
        }
    });
    return data
}

export const deletePortfolioQuery = async (id: any) => {
    await deleteTagsBeforeModify(id, "portfolio")
    const data = await db.portfolio.delete({
        where: {
            id
        }
    });
    return data
}

//Seo Queries
export const createSeoQuery = async (seo: any) => {

    const page = seo.slug
    const { slug, ...seoData } = seo

    const data = await db.seo.create({
        data: {
            ...seoData,
            page
        }
    });
    return data
}

export const getSeoQuery = async () => {
    const data = await db.seo.findMany();

    return data
}

export const getSingleSeoQuery = async (slug: string) => {
    const data = await db.seo.findFirst({
        where: {
            page: slug
        }
    })

    return data
}

export const updateSeoQuery = async (seo: any, sid: any) => {
    const page = seo.slug
    const { id, slug, ...seoData } = seo
    const data = await db.seo.update({
        data: {
            ...seoData,
            page
        },
        where: {
            id: sid
        }
    });
    return data
}

export const deleteSeoQuery = async (id: any) => {
    const data = await db.seo.delete({
        where: {
            id
        }
    });
    return data
}

//Project Queries

export const createProjectQuery = async (project: any) => {
    try {
        const { tags, ...projectData } = project;
        const data = await db.project.create({
            data: {
                ...projectData,
                tags: {
                    createMany: {
                        data: tags
                    }
                }
            },
            include: {
                tags: true,
                images: true
            }
        });
        return data
    } catch (error) {
        console.log("error on create project", error)
    }
}

export const getProjectsQuery = async () => {
    const data = await db.project.findMany({
        orderBy: {
            createdAt: "desc",
        },
        include: {
            images: true,
            tags: true
        }
    });

    return data
}

export const getProjectQuery = async (slug: string) => {
    const data = await db.project.findUnique({
        where: {
            slug,
        },
        include: {
            images: true,
            tags: true
        },
    });
    return data
}

export const updateProjectQuery = async (project: any, id: any) => {
    await deleteTagsBeforeModify(id, "project")
    const { images, ...projectData } = project
    const data = await db.project.update({
        where: { id },
        data: {
            ...projectData,
            tags: {
                createMany: {
                    data: projectData.tags
                }
            }
        }
    })

    return data
}

export const deleteProjectQuery = async (id: any) => {
    await deleteTagsBeforeModify(id, "project")
    const data = await db.project.delete({
        where: {
            id
        }
    })

    return data
}

//Project Queries

export const createTagQuery = async (tag: any) => {
    const data = await db.tag.create({
        data: {
            ...tag
        }
    });
    return data
}

export const getTagsQuery = async () => {
    const data = await db.tag.findMany({});

    return data
}

export const getTagQuery = async (id: number) => {
    const data = await db.tag.findUnique({
        where: {
            id: id
        }
    });
    return data
}

export const deleteTagQuery = async (id: any) => {
    const data = await db.tag.delete({
        where: {
            id
        }
    })

    return data
}

//MoreLinks Query

export const createMoreLinkQuery = async (more: any) => {
    try {
        const { tags, ...moreData } = more;
        const data = await db.more.create({
            data: {
                ...moreData,
                tags: {
                    createMany: {
                        data: tags
                    }
                }
            },
            include: {
                tags: true,
            }
        });
        return data
    } catch (error) {
        console.log("error on create project", error)
    }
}

export const getMoreLinksQuery = async () => {
    const data = await db.more.findMany({
        orderBy: {
            createdAt: "asc",
        },
        include: {
            tags: true,
        }
    });

    return data
}

export const getMoreLinkQuery = async (slug: string) => {
    const data = await db.more.findFirst({
        where: {
            OR: [
                { slug },
                { href: '/' + slug }
            ]
        },
        include: {
            images: true,
            tags: true,
            contents: true,
        },
    });


    return data
}


export const updateMoreLinkQuery = async (more: any, id: any) => {
    await deleteTagsBeforeModify(id, "more")
    const { images, contents, ...moreData } = more

    const data = await db.more.update({
        where: { id },
        data: {
            ...moreData,

            tags: {
                createMany: {
                    data: moreData.tags
                }
            }
        }
    })

    return data
}

export const deleteMoreLinkQuery = async (id: any) => {
    await deleteTagsBeforeModify(id, "more")
    const data = await db.more.delete({
        where: {
            id
        }
    })

    return data
}

//images Query
export const createImageQuery = async (image: any, page: string, pageId: any) => {
    let field = await connectKare(page, pageId)
    const data = await db.image.create({
        data: {
            url: image,
            ...field
        }
    });
    return data
}


// content Query

export const createContentQuery = async (content: any, page: any, id: any) => {
    const connect = await connectKare(page, id)
    const contents = await db.content.create({
        data: {
            ...content,
            type: content?.featureImage ? "image" : "text",
            ...connect
        }
    });
    return contents
}

export const updateContentQuery = async (content: any) => {
    const id = content.id
    delete content.id
    const contents = await db.content.update({
        data: {
            ...content
        },
        where: {
            id: content.id
        }
    });
    return contents
}

export const getContentsQuery = async () => {
    const contents = await db.content.findMany({});
    return contents
}

export const getContentQuery = async (id: any) => {
    const contents = await db.content.findUnique({
        where: {
            id
        }
    });
    return contents
}

export const deleteContentQuery = async (id: any) => {
    const contents = await db.content.delete({
        where: {
            id
        }
    });
    return contents
}

//Category Query

export const createCategoryQuery = async (category: any) => {
    const categor = await db.category.create({
        data: {
            ...category,
            tags: {
                createMany: {
                    data: category.tags
                }
            }
        }
    });
    return categor
}

export const updateCategoryQuery = async (category: any) => {
    const id = category.id
    delete category.id
    delete category.images
    const categor = await db.category.update({
        data: {
            ...category,
            tags: {
                createMany: {
                    data: category.tags
                }
            },
        },
        where: {
            id: id
        }
    });
    return categor
}

export const getCategorysQuery = async () => {
    const category = await db.category.findMany({});
    return category
}

export const getCategoryQuery = async (slug: any) => {
    const category = await db.category.findUnique({
        where: {
            slug
        }
    });
    return category
}

export const deleteCategoryQuery = async (id: any) => {
    const category = await db.category.delete({
        where: {
            id
        }
    });
    return category
}

// Blog Query

export const createBlogQuery = async (blog: any) => {
    const categoryId = parseInt(blog.category)
    delete blog.images
    delete blog.category
    const blogr = await db.blog.create({
        data: {
            ...blog,
            tags: {
                createMany: {
                    data: blog.tags
                },

            }
        }
    });

    if (categoryId) {
        await db.category.update({
            where: {
                id: categoryId
            },
            data: {
                blogs: {
                    connect: {
                        id: blogr.id
                    }
                }
            }
        })
    }

    return blogr
}

export const updateBlogQuery = async (blog: any, id: any) => {
    const catid = blog.category
    delete blog.category
    delete blog.images
    const blogr = await db.blog.update({
        data: {
            ...blog,
            tags: {
                createMany: {
                    data: blog.tags
                }
            },
            categories: {
                connect: {
                    id: parseInt(catid)
                }
            },
        },
        where: {
            id: id
        }
    });
    return blogr
}

export const getBlogsQuery = async () => {
    const blog = await db.blog.findMany({});
    return blog
}

export const getBlogQuery = async (slug: any) => {
    const blog = await db.blog.findUnique({
        where: {
            slug
        },
        include: {
            tags: true,
            images: true
        }
    });
    return blog
}

export const deleteBlogQuery = async (id: any) => {
    const blog = await db.blog.delete({
        where: {
            id
        }
    });
    return blog
}