"use server";
import { getMoreLinks } from "@/actions/more";
import DataTable from "@/components/admin/common/data-table";
import PageHeader from "@/components/admin/common/page-header";
import ViewButtonTable from "@/components/admin/common/view-button-table";
import { moreLinksField } from "@/lib/staticdata";
import React from "react";

type Props = {};

const MorePage = async (props: Props) => {
  const more: any = [];
  const data: any = await getMoreLinks();

  data.map((item: any) => {
    more.push({
      title: item.title,
      slug: item.slug,
      href: item.href,
      status: item.status,
      createdAt: item.createdAt,
      action: <ViewButtonTable page={"more"} slug={item.slug} />,
    });
  });
  return (
    <div className="p-8 flex flex-col justify-center items-center gap-y-8">
      <PageHeader page="more" fields={moreLinksField} />
      <DataTable data={more} />
    </div>
  );
};

export default MorePage;
