"use client";
import React, { useState } from "react";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import moment from "moment";

interface DatatableProps<T> {
  data: T[]; // Array of objects for the table
  actions?: React.ReactNode;
  page?: string;
}

function Datatable<T>({ data, page, actions }: DatatableProps<T>) {
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState("");
  const [visibleColumns, setVisibleColumns] = useState<string[]>(
    data.length > 0 ? (Object.keys(data[0] as object) as string[]) : []
  );

  if (data.length === 0) {
    return (
      <div className="min-w-7xl">
        <p className="text-gray-500 dark:text-gray-400">No data available</p>
      </div>
    );
  }

  const headers = Object.keys(data[0] as object) as (keyof T)[];

  const formatCellValue = (value: any): string | React.ReactNode => {
    if (value instanceof Date) {
      //return value.toLocaleDateString();
      return moment(value).format("DD/MMM/YYYY, hh:mm a");
    }
    return value;
  };

  // Filter data based on the search query
  const filteredData = data.filter((row) =>
    headers.some((header) =>
      String(row[header]).toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  // Pagination Logic
  const totalPages = Math.ceil(filteredData.length / rowsPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage
  );

  const handleRowsPerPageChange = (value: string) => {
    setRowsPerPage(Number(value));
    setCurrentPage(1);
  };

  const toggleColumnVisibility = (column: string) => {
    setVisibleColumns(
      (prev) =>
        prev.includes(column)
          ? prev.filter((col) => col !== column) // Remove column
          : [...prev, column] // Add column
    );
  };

  return (
    <div className="w-[95vw] mx-auto bg-gray-900 border px-4 border-gray-300 dark:border-gray-600 rounded-lg">
      {/* Search Bar */}
      <div className="flex justify-between items-center p-4">
        <Input
          type="text"
          placeholder="Search..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-1/3"
        />
        {/* Custom Multi-Select Column Dropdown */}
        {/* <div className="relative">
          <Button variant="outline" className="w-40">
            Select Columns
          </Button>
          <div className="absolute left-0 mt-2 w-40 bg-white dark:bg-gray-800 shadow-md rounded-lg z-10">
            <div className="p-2">
              {headers.map((header, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={visibleColumns.includes(header.toString())}
                    onChange={() => toggleColumnVisibility(header.toString())}
                  />
                  <span className="text-gray-700 dark:text-gray-300">
                    {typeof header === "string" || typeof header === "number"
                      ? header
                      : ""}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div> */}
      </div>

      <Table className="min-w-7xl text-sm">
        <TableHeader>
          <TableRow>
            <TableHead className="text-gray-700 dark:text-gray-300">
              #
            </TableHead>
            {headers
              .filter((header) => visibleColumns.includes(header.toString()))
              .map((header, index) => (
                <TableHead
                  key={index}
                  className="text-gray-700 dark:text-gray-300 capitalize"
                >
                  {header.toString()}
                </TableHead>
              ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {paginatedData.map((row, rowIndex) => (
            <TableRow
              key={rowIndex}
              className={
                rowIndex % 2 === 0
                  ? "bg-gray-100 dark:bg-gray-800"
                  : "bg-white dark:bg-gray-900"
              }
            >
              <TableCell>{rowIndex + 1}</TableCell>
              {headers
                .filter((header) => visibleColumns.includes(header.toString()))
                .map((header, colIndex) => (
                  <TableCell
                    key={colIndex}
                    className="py-2 px-4 text-gray-800 dark:text-gray-300"
                  >
                    {formatCellValue(row[header])}
                  </TableCell>
                ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination and Rows Per Page */}
      <div className="flex justify-between items-center p-4 border-t border-gray-300 dark:border-gray-600">
        <div className="flex items-center">
          <label className="text-gray-700 dark:text-gray-300">
            Show entries
            <Select onValueChange={handleRowsPerPageChange} defaultValue="10">
              <SelectTrigger className="ml-2 w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </label>
        </div>
        <div className="text-gray-600 dark:text-gray-400">
          Page {currentPage} of {totalPages}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            variant="secondary"
          >
            Previous
          </Button>
          <span
            className={`px-4 py-2 ${
              currentPage === totalPages ? "bg-gray-600" : "bg-gray-800"
            } rounded`}
          >
            {currentPage}
          </span>
          <Button
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
            variant="secondary"
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}

export default Datatable;
