"use client";
import React from "react";
import { pricing } from "@/lib/staticdata";
import { CheckIcon } from "lucide-react";
import { BackgroundGradient } from "@/components/ui/background-gradient";
type Props = {};

const PricingMain = (props: Props) => {
  return (
    <>
      <section className="pt-4 pb-16">
        <div className="w-full lg:max-w-7xl mx-auto">
          {pricing &&
            pricing.map((price) => (
              <div key={price.id}>
                <div className="text-center my-8">
                  <h2 className="text-2xl font-semibold">{price.category}</h2>
                </div>

                <div className="p-4 lg:p-0 flex flex-col lg:flex-row justify-center gap-8">
                  {price.packages &&
                    price.packages.map((pkg) => (
                      <BackgroundGradient
                        key={pkg.id}
                        className="h-full rounded-[22px] w-full lg:max-w-sm p-4 sm:p-10 bg-white dark:bg-zinc-900 backdrop-blur-lg"
                      >
                        <h4 className="text-lg font-semibold text-center mb-4">
                          {pkg.title}
                        </h4>
                        <div className="w-full flex items-center justify-center mb-4 space-x-4">
                          <h2 className="text-4xl text-center font-bold text-gray-300">
                            &#8377;{pkg.price}
                          </h2>
                        </div>
                        <div className="text-center mb-4">
                          <p className="text-sm">{pkg.description}</p>
                        </div>
                        <ul className="space-y-2">
                          {pkg.features &&
                            pkg.features.map((feature, index) => (
                              <li
                                className="flex items-center gap-4"
                                key={index}
                              >
                                <CheckIcon
                                  className="text-green-400"
                                  strokeWidth={3}
                                  size={20}
                                />{" "}
                                {feature}
                              </li>
                            ))}
                        </ul>
                        <div className="text-center mt-6">
                          <a
                            href="contact"
                            className="inline-block bg-indigo-600 text-white py-2 px-6 rounded-lg hover:bg-indigo-500"
                          >
                            Get Started
                          </a>
                        </div>
                      </BackgroundGradient>
                    ))}
                </div>
              </div>
            ))}
        </div>
      </section>
    </>
  );
};

export default PricingMain;
