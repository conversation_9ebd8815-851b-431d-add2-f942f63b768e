"use server";
import { getMoreLink } from "@/actions/more";
import MoreLinks from "@/components/main/services/More";
import { generateMetadata1 } from "@/lib/utils";
import React from "react";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};

export async function generateMetadata({ params }: Props) {
  const { slug } = await params;
  return generateMetadata1({
    page: slug.toLowerCase(),
  });
}

const MoreLinksPage = async ({ params }: Props) => {
  const { slug } = await params;
  const items = await getMoreLink(slug);

  return (
    <>
      <div className="mt-24">
        <MoreLinks data={items} />
      </div>
    </>
  );
};

export default MoreLinksPage;
