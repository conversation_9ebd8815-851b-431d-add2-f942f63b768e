"use client";

import { getBlog, getBlogs, restoreBlog } from "@/actions/blog";
import { restoreParent } from "@/actions/parent";
import { getPortfolios, restorePortfolio } from "@/actions/portfolio";
import { generateSEO, getPageSeo, getSeo, restoreSeo } from "@/actions/seo";
import { getServices, restoreService } from "@/actions/service";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import toast from "react-hot-toast";
import { restoreContent } from "@/actions/content";
import slugify from "react-slugify";
import {
  getMoreLink,
  getMoreLinks,
  updateMoreLink,
  updateMoreSlugLink,
} from "@/actions/more";

export default function SettingsPage() {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isPublicProfile, setIsPublicProfile] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(false);

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    // Here you would typically send the form data to your backend
    console.log("Settings saved:", {
      isDarkMode,
      isPublicProfile,
      emailNotifications,
      pushNotifications,
    });
  };
  const handlRestore = async () => {
    const seo = await restoreSeo();
    if (seo) {
      toast.success("Restored Seo Successfully");
    }
    const parent = await restoreParent();
    if (parent) {
      toast.success("Restored Parent Successfully");
    }
    const service = await restoreService();
    if (service) {
      toast.success("Restored Service Successfully");
    }
    const portfolio = await restorePortfolio();
    if (portfolio) {
      toast.success("Restored Portfolio Successfully");
    }
    const blog = await restoreBlog();
    if (blog) {
      toast.success("Restored Blog Successfully");
    }
    const more = await restoreContent();
    if (more) {
      toast.success("Restored more Successfully");
    }
  };

  const handleBlogSeoGenerate = async () => {
    const blogSeo = await getBlogs();

    const getAll = Promise.all(
      blogSeo.map(async (blog: any) => {
        const x = await generateSEO(
          blog.title,
          blog.description,
          "blog",
          blog.slug
        );
        console.log(x, "ss");
      })
    );

    console.log(getAll, "getAll");
    toast.success("Blog Seo Generated Successfully");
  };

  const handleServiceSeoGenerate = async () => {
    const ServiceSeo = await getServices();

    const getAll = Promise.all(
      ServiceSeo.map(async (service: any) => {
        const x = await generateSEO(
          service.title,
          service.description,
          "service",
          service.slug
        );
        console.log(x, "ss");
      })
    );

    console.log(getAll, "getAll");
    toast.success("Services Seo Generated Successfully");
  };

  const handlePortfolioSeoGenerate = async () => {
    const PortfolioSeo = await getPortfolios();

    const getAll = Promise.all(
      PortfolioSeo.map(async (portfolio: any) => {
        const x = await generateSEO(
          portfolio.title,
          portfolio.description,
          "portfolio",
          portfolio.slug
        );
      })
    );

    toast.success("Portfolio Seo Generated Successfully");
  };

  const handlePageSeoGenerate = async () => {
    const PageSeo = await getPageSeo();

    const getAll = Promise.all(
      PageSeo.map(async (page: any) => {
        await generateSEO(
          page.title,
          page.description ||
            "we provide website design, web app development mobile app developent, seo and digital marketing etc services",
          "page",
          slugify(page.page),
          page.url
        );
      })
    );

    if (await getAll) {
      toast.success("Page Seo Generated Successfully");
    }
  };

  const handleMoreSeoGenerate = async () => {
    const MoreSeo = await getMoreLinks();

    const getAll = Promise.all(
      MoreSeo.map(async (page: any) => {
        await generateSEO(
          page.title == "wedsite"
            ? "wedding website design and development services"
            : "website design and development services in " + page.title,
          "",
          "more",
          page.slug,
          "/more" + page.href
        );
      })
    );

    if (await getAll) {
      toast.success("More Link Seo Generated Successfully");
    }
  };

  const updatMoreSlugChange = async () => {
    const more = await getMoreLinks();

    const getData = Promise.all(
      more.map(async (ml) => {
        await updateMoreSlugLink(ml.slug, ml.href);
      })
    );

    if (await getData) {
      toast.success("More links are updated check database");
    }
  };

  return (
    <div className="w-full mx-auto p-10">
      <h1 className="text-3xl font-bold mb-6">Settings</h1>
      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="create-generate">
            Restore, Backup and Generate
          </TabsTrigger>
        </TabsList>
        <form onSubmit={handleSubmit}>
          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Manage your general preferences.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="dark-mode">Dark Mode</Label>
                  <Switch
                    id="dark-mode"
                    checked={isDarkMode}
                    onCheckedChange={setIsDarkMode}
                  />
                </div>

                <div className="space-y-1">
                  <Label htmlFor="language">Language</Label>
                  <Select defaultValue="en">
                    <SelectTrigger id="language">
                      <SelectValue placeholder="Select Language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="es">Español</SelectItem>
                      <SelectItem value="fr">Français</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end mt-6">
                <Button type="submit">Save Changes</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          <TabsContent value="account">
            <Card>
              <CardHeader>
                <CardTitle>Account Settings</CardTitle>
                <CardDescription>
                  Manage your account details and preferences.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-1">
                  <Label htmlFor="username">Username</Label>
                  <Input id="username" defaultValue="johndoe" />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    defaultValue="<EMAIL>"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="public-profile">Public Profile</Label>
                  <Switch
                    id="public-profile"
                    checked={isPublicProfile}
                    onCheckedChange={setIsPublicProfile}
                  />
                </div>
              </CardContent>
              <CardFooter className="flex justify-end mt-6">
                <Button type="submit">Save Changes</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle>Notification Preferences</CardTitle>
                <CardDescription>
                  Manage how you receive notifications.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="email-notifications">
                    Email Notifications
                  </Label>
                  <Switch
                    id="email-notifications"
                    checked={emailNotifications}
                    onCheckedChange={setEmailNotifications}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="push-notifications">Push Notifications</Label>
                  <Switch
                    id="push-notifications"
                    checked={pushNotifications}
                    onCheckedChange={setPushNotifications}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="create-generate">
            <Card>
              <CardHeader>
                <CardTitle>Backup and Generate</CardTitle>
                <CardDescription>
                  Manage your backup and generate settings.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Restore Old</Label>
                  <Button onClick={handlRestore} type="button">
                    Restore
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <Label>Generate Blog Seo</Label>
                  <Button onClick={handleBlogSeoGenerate} type="button">
                    Generate
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <Label>Generate Service Seo</Label>
                  <Button onClick={handleServiceSeoGenerate} type="button">
                    Generate
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <Label>Generate Portfolio Seo</Label>
                  <Button onClick={handlePortfolioSeoGenerate} type="button">
                    Generate
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <Label>Generate Page Seo</Label>
                  <Button onClick={handlePageSeoGenerate} type="button">
                    Generate
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <Label>Generate More Seo</Label>
                  <Button onClick={handleMoreSeoGenerate} type="button">
                    Generate
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <Label>Change More</Label>
                  <Button onClick={updatMoreSlugChange} type="button">
                    Update
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </form>
      </Tabs>
    </div>
  );
}
