generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  name      String?
  password  String
  createdAt DateTime @default(now())
  updateAt  DateTime @updatedAt
}

model Business {
  id        Int      @id @default(autoincrement())
  phone     String?
  email     String?
  address   String?
  facebook  String?
  instagram String?
  youtube   String?
  twitter   String?
  linkedin  String?
  createdAt DateTime @default(now())
  updateAt  DateTime @updatedAt
}

model Contact {
  id        Int      @id @default(autoincrement())
  name      String?
  email     String?
  phone     String
  type      String?
  message   String?
  verified  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model About {
  id           Int      @id @default(autoincrement())
  title        String?
  featureImage String?
  description  String?
  link         String?
  alt          String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  images       Image[]
}

model Project {
  id           Int      @id @default(autoincrement())
  title        String
  slug         String   @unique
  description  String?
  logo         String?
  featureImage String?
  type         String?
  link         String?
  tags         Tag[]
  images       Image[]
  status       Int?     @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model More {
  id        Int       @id @default(autoincrement())
  title     String
  slug      String    @unique
  href      String?   @unique
  contents  Content[]
  images    Image[]
  status    Int?      @default(0)
  tags      Tag[]
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Content {
  id           Int      @id @default(autoincrement())
  title        String
  slug         String   @unique
  description  String
  featureImage String?
  featureVideo String?
  type         String?
  status       Int?     @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  more         More?    @relation(fields: [moreId], references: [id])
  moreId       Int?
}

model Portfolio {
  id           Int      @id @default(autoincrement())
  title        String
  slug         String   @unique
  client       String?
  description  String?
  folder       String?
  type         String?
  tags         Tag[]
  logo         String?
  alt          String?
  banner       String?
  bannerVideo  String?
  featureImage String?
  featureVideo String?
  link         String
  status       Int?     @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  images       Image[]
}

model Category {
  id           Int      @id @default(autoincrement())
  title        String
  slug         String   @unique
  featureImage String?
  tags         Tag[]
  status       Int?     @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  blogs        Blog[]
}

model Blog {
  id           Int       @id @default(autoincrement())
  title        String
  slug         String    @unique
  description  String
  featureImage String?
  featureVideo String?
  status       Int?      @default(0)
  tags         Tag[]
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  categories   Category? @relation(fields: [categoryId], references: [id])
  categoryId   Int?
  images       Image[]
}

model Parent {
  id           Int       @id @default(autoincrement())
  slug         String    @unique
  title        String
  description  String?
  featureImage String?
  status       Int?      @default(0)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  services     Service[]
}

model Service {
  id           Int      @id @default(autoincrement())
  title        String
  slug         String   @unique
  description  String
  featureImage String?
  featureVideo String?
  tags         Tag[]
  status       Int?     @default(1)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  parent       Parent?  @relation(fields: [parentId], references: [id])
  parentId     Int?
  images       Image[]
}

model Seo {
  id          Int      @id @default(autoincrement())
  type        String
  page        String   @unique
  title       String?  @unique
  description String?
  keywords    String?
  url         String   @unique
  status      Int?     @default(1)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Tag {
  id          Int        @id @default(autoincrement())
  text        String
  status      Int?       @default(1)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  more        More?      @relation(fields: [moreId], references: [id])
  moreId      Int?
  project     Project?   @relation(fields: [projectId], references: [id])
  projectId   Int?       @map("projectId")
  portfolio   Portfolio? @relation(fields: [portfolioId], references: [id])
  portfolioId Int?
  category    Category?  @relation(fields: [categoryId], references: [id])
  categoryId  Int?
  blog        Blog?      @relation(fields: [blogId], references: [id])
  blogId      Int?
  service     Service?   @relation(fields: [serviceId], references: [id])
  serviceId   Int?
}

model Image {
  id          Int        @id @default(autoincrement())
  type        String?
  url         String?
  video       String?
  alt         String?
  portfolio   Portfolio? @relation(fields: [portfolioId], references: [id], onDelete: Cascade)
  portfolioId Int?
  about       About?     @relation(fields: [aboutId], references: [id])
  aboutId     Int?
  blog        Blog?      @relation(fields: [blogId], references: [id])
  blogId      Int?
  service     Service?   @relation(fields: [serviceId], references: [id])
  serviceId   Int?
  project     Project?   @relation(fields: [projectId], references: [id])
  projectId   Int?
  more        More?      @relation(fields: [moreId], references: [id])
  moreId      Int?
  status      Int?       @default(1)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
}
