"use server";
import React from "react";
import DataTable from "@/components/admin/common/data-table";
import { getCategorys } from "@/actions/category";
import ViewButtonTable from "@/components/admin/common/view-button-table";
import PageHeader from "@/components/admin/common/page-header";
import { categoryField } from "@/lib/staticdata";

type Props = {};

const CategoryPage = async (props: Props) => {
  const categorys: any = [];
  const data: any = await getCategorys();

  data.map((item: any) => {
    categorys.push({
      title: item.title,
      slug: item.slug,
      status: item.status,
      createdAt: item.createdAt,
      action: <ViewButtonTable page={"category"} slug={item.slug} />,
    });
  });

  return (
    <div className="p-8 flex flex-col justify-center items-center gap-y-8">
      <PageHeader page="category" fields={categoryField} />
      <DataTable data={categorys} />
    </div>
  );
};

export default CategoryPage;
