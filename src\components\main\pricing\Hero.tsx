"use client";
import React from "react";
import { Cover } from "../../ui/cover";

type Props = {};

const PricingHero = (props: Props) => {
  return (
    <div className="h-[50vh] flex items-center justify-center">
      <h1 className="text-4xl md:text-4xl lg:text-6xl font-semibold max-w-7xl mx-auto text-center mt-6 relative z-20 py-6 bg-clip-text text-transparent bg-gradient-to-b from-neutral-800 via-neutral-700 to-neutral-700 dark:from-neutral-800 dark:via-white dark:to-white bg-slate-700">
        For You <br /> <Cover className="cursor-default">Our Pricing</Cover>
      </h1>
    </div>
  );
};

export default PricingHero;
