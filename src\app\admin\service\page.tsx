import Datatable from "@/components/admin/common/data-table";
import React from "react";
import Image from "next/image";
import ViewButtonTable from "@/components/admin/common/view-button-table";
import { getServices } from "@/actions/service";
import PageHeader from "@/components/admin/common/page-header";
import { parentOptionFormat } from "@/lib/utils";

type Props = {};

const ServicePage = async (props: Props) => {
  const services: any = [];
  const datas = await getServices();

  datas.map((item: any) => {
    services.push({
      title: item.title,
      thumbnail: (
        <Image
          priority
          src={
            item.featureImage
              ? item.featureImage
              : "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/preloader%2Fplaceholder.png?alt=media&token=bf1e9987-d53b-46ad-b92f-c877c3d40982"
          }
          alt={item.title}
          width={100}
          height={100}
        />
      ),
      description: item.description.substring(0, 150) + "...",
      slug: item.slug,
      type: item.type,
      status: item.status,
      createdAt: item.createdAt,
      parent: item.parent.title,
      action: <ViewButtonTable page={"service"} slug={item.slug} />,
    });
  });

  const servicesFields = [
    {
      name: "title",
      label: "Title",
      type: "text",
      placeholder: "Enter title",
      autoSlug: true,
    },
    {
      name: "slug",
      label: "Slug",
      type: "text",
      disabled: true,
      placeholder: "Auto-generated slug",
    },

    { name: "tags", label: "Tags", type: "tags", placeholder: "Add tags" },
    {
      name: "status",
      label: "Status",
      type: "select",
      options: [
        { value: 0, label: "Deactive" },
        { value: 1, label: "Active" },
      ],
      placeholder: "Project Status",
    },
    {
      name: "parent",
      label: "Parent",
      type: "select",
      options: (await parentOptionFormat()) || [],
      placeholder: "Parent Category",
    },
    {
      name: "description",
      label: "Description",
      type: "textarea",
      placeholder: "Enter description",
    },
    { name: "featureImage", label: "Feature Image", type: "upload" },
  ];
  return (
    <div className="p-8 flex flex-col justify-center items-center gap-y-8 w-full md:min-w-7xl">
      <PageHeader page={"service"} fields={servicesFields} />
      <Datatable data={services} />
    </div>
  );
};

export default ServicePage;
