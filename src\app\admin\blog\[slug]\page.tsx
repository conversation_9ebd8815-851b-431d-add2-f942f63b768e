"use server";
import { getBlog } from "@/actions/blog";
import React from "react";
import NotFound from "@/app/admin/not-found";
import BlogDetails from "@/components/admin/blog/blogDetails";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};
const BlogSlugPage = async ({ params }: Props) => {
  const { slug } = await params;
  const blog: any = await getBlog(slug);

  if (!blog) {
    return <NotFound />;
  }

  return (
    <>
      <BlogDetails blog={blog} />
    </>
  );
};

export default BlogSlugPage;
