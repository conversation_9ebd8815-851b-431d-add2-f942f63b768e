"use server";
import { db } from "@/lib/db";
import { createMoreLinkQuery, deleteMoreLinkQuery, getMoreLinkQuery, getMoreLinksQuery, updateMoreLinkQuery } from "@/lib/queries";

export const createMoreLink = async (moreLink: any) => {
    const data = await createMoreLinkQuery(moreLink);
    return data
}

export const getMoreLinks = async () => {
    const moreLinks = await getMoreLinksQuery();
    return moreLinks
}

export const getMoreLink = async (slug: string) => {
    const moreLink = await getMoreLinkQuery(slug);
    return moreLink
}

export const updateMoreLink = async (moreLink: any, id: any) => {
    const data = await updateMoreLinkQuery(moreLink, id);
    return data
}

export const updateMoreSlugLink = async (slug: any, id: any) => {
    console.log(slug, id)
    const data = await db.seo.update({
        data: {
            page: slug,
        },
        where: {
            url: "/more" + id
        }
    })
    console.log(data)
    return data
}


export const deleteMoreLink = async (id: any) => {
    const data = await deleteMoreLinkQuery(id);
    return data
}