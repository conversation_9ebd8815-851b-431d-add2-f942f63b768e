"use client";
import React, { use, useEffect, useRef, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  LayoutDashboard,
  FileText,
  Settings,
  BoxIcon,
  Rss,
  WrenchIcon,
  Link2,
  LogOut,
  X,
  SearchIcon,
  Home,
} from "lucide-react";

import {
  IconCategory,
  IconCategoryPlus,
  IconDeviceMobile,
} from "@tabler/icons-react";
import { AnimatePresence, motion } from "framer-motion";
import { useSession, useClerk, useUser, UserButton } from "@clerk/nextjs";
import { useRouter } from "next/navigation";

type Props = {};

const FloatBar = (props: Props) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [isDockVisible, setIsDockVisible] = useState(false);
  const dockRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  const { isSignedIn } = useSession();
  const { signOut } = useClerk();
  const { user } = useUser();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dockRef.current && !dockRef.current.contains(event.target as Node)) {
        setIsDockVisible(false);
      }
    };

    if (isDockVisible) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDockVisible]);

  const handleSignOut = async () => {
    await signOut();
    router.push("/admin/");
  };

  const toggleDock = () => {
    setIsDockVisible(!isDockVisible);
  };

  const leftMenuItems = [
    {
      icon: (
        <Home className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      label: "Home",
      href: "/",
    },
    {
      icon: (
        <LayoutDashboard className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      label: "Dashboard",
      href: "/admin",
    },
    {
      icon: (
        <Settings className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      label: "Settings",
      href: "/admin/setting",
    },
  ];

  const menuItems = [
    {
      id: 1,
      name: "Project",
      submenu: [
        {
          icon: (
            <BoxIcon className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
          ),
          label: "Projects",
          href: "/admin/project",
        },
      ],
    },

    {
      id: 2,
      name: "PortFolio",
      submenu: [
        {
          icon: (
            <FileText className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
          ),
          label: "Portfolio",
          href: "/admin/portfolio",
        },
      ],
    },
    {
      id: 3,
      name: "Seo",
      submenu: [
        {
          icon: (
            <SearchIcon className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
          ),
          label: "Seo",
          href: "/admin/seo",
        },
      ],
    },
    {
      id: 4,
      name: "contact",
      submenu: [
        {
          icon: (
            <IconDeviceMobile className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
          ),
          label: "Contact",
          href: "/admin/contact",
        },
      ],
    },
    {
      id: 5,
      name: "Services",
      submenu: [
        {
          icon: (
            <IconCategory className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
          ),
          label: "Parent",
          href: "/admin/parent",
        },

        {
          icon: (
            <WrenchIcon className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
          ),
          label: "Services",
          href: "/admin/service",
        },
      ],
    },
    {
      id: 6,
      name: "Blog",
      submenu: [
        {
          icon: (
            <IconCategoryPlus className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
          ),
          label: "Category",
          href: "/admin/category",
        },
        {
          icon: (
            <Rss className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
          ),
          label: "Blog",
          href: "/admin/blog",
        },
      ],
    },
    {
      id: 7,
      name: "Misc",
      submenu: [
        {
          icon: (
            <Link2 className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
          ),
          label: "More",
          href: "/admin/more",
        },
      ],
    },
  ];

  return (
    <div>
      <AnimatePresence>
        {isDockVisible && (
          <motion.div
            ref={dockRef}
            onMouseLeave={() => setHoveredIndex(null)}
            // onClick={() => setIsDockVisible(false)}
            className="z-[99999] w-[90vw] h-[70vh] lg:w-[60vw] lg:h-[80vh] fixed bottom-20 left-5 flex items-start justify-start space-x-2 p-4 rounded-2xl bg-black bg-opacity-90 backdrop-blur-md shadow-lg"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            transition={{ duration: 0.3 }}
          >
            <div className="h-full w-full flex flex-col items-center">
              <div className="h-full w-full grid grid-cols-3">
                <div className="h-full w-full flex flex-col gap-4 items-start justify-end">
                  {leftMenuItems.map((item, index) => (
                    <Link
                      onClick={() => toggleDock()}
                      target={item.label === "Home" ? "_blank" : ""}
                      key={index}
                      className="flex items-center justify-start gap-2  group/sidebar py-0"
                      href={item.href}
                    >
                      {item.icon}
                      <span className="text-neutral-700 dark:text-neutral-200 text-sm group-hover/sidebar:translate-x-1 transition duration-150 whitespace-pre inline-block !p-0 !m-0">
                        {item.label}
                      </span>
                    </Link>
                  ))}
                  {isSignedIn && (
                    <button
                      onClick={() => handleSignOut()}
                      className="flex items-center justify-start gap-2  group/sidebar py-0"
                    >
                      <LogOut
                        className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0"
                        size={20}
                      />{" "}
                      <span className="text-neutral-700 dark:text-neutral-200 text-sm group-hover/sidebar:translate-x-1 transition duration-150 whitespace-pre inline-block !p-0 !m-0">
                        Logout
                      </span>
                    </button>
                  )}
                </div>
                <div className="h-full w-auto md:col-span-2  gap-4 items-start justify-center border-l-4 pl-4 mr-2">
                  <div className="grid grid-cols-3 gap-4">
                    {menuItems.map((item) => (
                      <div key={item.id}>
                        <span className="underline font-bold text-xl">
                          {item.name}
                        </span>
                        <div className=" flex flex-col gap-1">
                          {item.submenu.map((item, index) => (
                            <Link
                              onClick={() => toggleDock()}
                              key={index}
                              className="flex items-center justify-start gap-2 group/sidebar pt-2"
                              href={item.href}
                            >
                              {item.icon}
                              <span className="text-neutral-700 dark:text-neutral-200 text-sm group-hover/sidebar:translate-x-1 transition duration-150 whitespace-pre inline-block !p-0 !m-0">
                                {item.label}
                              </span>
                            </Link>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      {/* <UserButton /> */}
      <motion.button
        className="z-[9999] fixed bottom-4 left-4 bg-white text-black bg-opacity-90 rounded-full p-0 shadow-lg"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={toggleDock}
      >
        {!isDockVisible ? (
          <Image
            src={"/favicon.ico"}
            alt="Toggle Menu"
            width={24}
            height={24}
            className="w-16 h-16 object-cover rounded-full"
          />
        ) : (
          <X size={24} className="w-6 h-6 m-3" />
        )}
      </motion.button>
    </div>
  );
};

export default FloatBar;
