"use client";
import Image from "next/image";
import React, { useState } from "react";
import { cn } from "@/lib/utils";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Trigger,
} from "./animated-modal";
import { ExternalLink } from "lucide-react";
import Link from "next/link";

export const Card = React.memo(
  ({
    card,
    index,
    hovered,
    setHovered,
  }: {
    card: any;
    index: number;
    hovered: number | null;
    setHovered: React.Dispatch<React.SetStateAction<number | null>>;
  }) => (
    <div
      onMouseEnter={() => setHovered(index)}
      onMouseLeave={() => setHovered(null)}
      className={cn(
        "rounded-lg relative bg-gray-100 dark:bg-neutral-900 overflow-hidden h-72 lg:h-96 w-full transition-all duration-300 ease-out",
        hovered !== null && hovered !== index && "blur-sm scale-[0.98]"
      )}
    >
      <Image
        priority
        src={card.src}
        alt={card.title}
        fill
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        className="w-full h-full object-cover"
        style={{
          transition: hovered === index ? "4s ease" : "6s ease",
          objectPosition: hovered === index ? "center bottom" : "center top",
        }}
      />

      <div
        className={cn(
          "absolute inset-0 bg-black/50 flex items-end py-8 px-4 transition-opacity duration-300",
          hovered === index ? "opacity-100" : "opacity-0"
        )}
      >
        <div className="capitalize text-xl lg:text-2xl font-medium bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-200">
          {card.title}
        </div>
      </div>
    </div>
  )
);

Card.displayName = "Card";

type Card = {
  title: string;
  src: string;
  link: string;
  tags: any;
  type: string;
  slug: string;
};

export function FocusCards({ cards }: { cards: Card[] }) {
  const [hovered, setHovered] = useState<number | null>(null);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-10 max-w-5xl mx-auto lg:px-8 w-full">
      {cards.map((card, index) => (
        <Modal key={index}>
          <ModalTrigger>
            <Card
              key={card.title}
              card={card}
              index={index}
              hovered={hovered}
              setHovered={setHovered}
            />
          </ModalTrigger>
          <ModalBody>
            <ModalContent>
              <h3 className="text-lg lg:text-3xl capitalize text-neutral-600 dark:text-neutral-100 font-bold text-center mb-2">
                {card.title}
              </h3>
              <h4 className="text-lg text-center lg:text-xl capitalize font-bold mb-2  text-gray-800 bg-gray-300 rounded-full">
                {card.type}
              </h4>
              <Image
                priority
                onMouseEnter={() => setHovered(index)}
                onMouseLeave={() => setHovered(null)}
                src={card.src}
                alt="thetechfossil, the tech fossil, website development company"
                width="500"
                height="500"
                className="rounded-lg h-72 w-full lg:h-72 lg:w-full object-cover"
                style={{
                  transition: hovered === index ? "4s ease" : "6s ease",
                  objectPosition:
                    hovered === index ? "center bottom" : "center top",
                }}
              />
              <div className="flex flex-wrap justify-center gap-2 mt-4">
                {card.tags.map((tag: any, index: number) => (
                  <span
                    key={tag.id}
                    className="capitalize px-3 py-1.5 text-md shadow-lg shadow-cyan-500/40 text-cyan-800 bg-cyan-100 rounded-full"
                  >
                    {tag.text}
                  </span>
                ))}
              </div>
            </ModalContent>
            <ModalFooter className="-mt-6">
              <Link
                href={card.link}
                target="_blank"
                className="w-full h-12 flex justify-center items-center bg-black text-white dark:bg-white dark:text-black text-lg px-2 py-1 rounded-md border border-black "
              >
                Visit Website
                <ExternalLink size={16} strokeWidth={3} className="ml-4" />
              </Link>
              <Link
                target="_blank"
                className="w-full h-12 ml-2 flex justify-center items-center bg-white text-black dark:bg-blue-500 dark:text-white text-lg px-2 py-1 rounded-md border border-black "
                href={"/portfolio/" + card.slug}
              >
                Read More
              </Link>
            </ModalFooter>
          </ModalBody>
        </Modal>
      ))}
    </div>
  );
}
