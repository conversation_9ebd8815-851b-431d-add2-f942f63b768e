"use server";
import { createCategoryQuery, deleteCategoryQuery, getCategoryQuery, getCategorysQuery, updateCategoryQuery } from "@/lib/queries";

export const createCategory = async (category: any) => {
    const data = await createCategoryQuery(category);
    return data;
};

export const getCategory = async (slug: string) => {
    const data = getCategoryQuery(slug);
    return data;
};

export const getCategorys = async () => {
    const category = await getCategorysQuery();
    return category;
}

export const updateCategory = async (category: any) => {
    const updatedCategory = await updateCategoryQuery(category)
    return updatedCategory;
}

export const deleteCategory = async (id: any) => {
    const data = await deleteCategoryQuery(id)
    return data
}