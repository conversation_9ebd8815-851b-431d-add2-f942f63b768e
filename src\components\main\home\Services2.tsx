"use client";

import { Dot } from "lucide-react";
import React, { useState } from "react";
import { servicesContent, servicesName } from "../../../lib/staticdata";
import ContactModal from "../common/contact-modal";

type Props = {};

const HomeServices2 = (props: Props) => {
  const [services, setServices] = useState("creativity");

  return (
    <div id="services-02">
      <div className="h-full lg:h-screen py-16 lg:p-0 bg-gray-300">
        {/* Tab Navigation */}
        <ul className="py-8 uppercase font-semibold flex flex-col lg:flex-row justify-center text-center space-y-4 lg:space-y-0 lg:space-x-6">
          {servicesName &&
            servicesName.map((service) => (
              <li
                key={service.id}
                className={`${
                  service.name === services
                    ? "text-blue-500 font-bold underline"
                    : "cursor-pointer text-gray-700"
                } flex items-center justify-center gap-x-4`}
              >
                <a onClick={() => setServices(service.name)}>{service.name}</a>
                {service.id === servicesName.length ? <></> : <Dot size={20} />}
              </li>
            ))}
        </ul>

        {/* Tab Content */}
        {servicesContent &&
          servicesContent.map(
            (service, index) =>
              services === service.servicesName && (
                <div
                  key={service.id}
                  style={{
                    backgroundImage: `url(${service.image})`,
                  }}
                  className="h-full bg-cover bg-center bg-fixed bg-no-repeat"
                >
                  <div className="bg-gray-900 bg-opacity-50 h-full flex items-center justify-center">
                    <div className="w-full lg:max-w-7xl mx-auto items-center">
                      <div className="bg-white/70 backdrop-blur-md p-6 shadow-md rounded-lg">
                        <h2 className="text-4xl font-bold text-gray-800">
                          0{index + 1}
                        </h2>
                        <h3 className="text-2xl font-semibold text-gray-700 mt-2">
                          {service.title}
                        </h3>
                        <p className="text-gray-600 mt-4">
                          {service.description}
                        </p>
                        <div className="mt-6">
                          <ContactModal />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
          )}
      </div>
    </div>
  );
};

export default HomeServices2;
