"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import Modal from "@/components/admin/common/form-modal";

interface FormModalProps {
  title: string;
  buttonText: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
}

function FormModal({ title, buttonText, children, icon }: FormModalProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <>
      <Button onClick={openModal}>
        {icon} {buttonText}
      </Button>
      <Modal isOpen={isModalOpen} onClose={closeModal} title={title}>
        {children}
      </Modal>
    </>
  );
}

export default FormModal;
