"use server";
import { db } from "@/lib/db";
import { createContent<PERSON><PERSON><PERSON>, deleteContent<PERSON><PERSON><PERSON>, getContent<PERSON><PERSON>y, getContentsQuery, updateContentQuery } from "@/lib/queries";
import { moreLinks } from "@/lib/staticdata";
import slugify from "react-slugify";

export const createContent = async (content: any, page: any, id: any) => {
    const data = await createContentQuery(content, page, id);
    return data;
};

export const getContent = async (slug: string) => {
    const data = getContentQuery(slug);
    return data;
};

export const getContents = async () => {
    const content = await getContentsQuery();
    return content;
}

export const updateContent = async (content: any) => {
    const updatedContent = await updateContentQuery(content)
    return updatedContent;
}

export const deleteContent = async (id: any) => {
    const data = await deleteContentQuery(id)
    return data
}

export const restoreContent = async () => {
    try {
        const storedMore = await Promise.all(
            moreLinks.map(async (link) => {
                const more = await db.more.create({
                    data: {
                        title: link.title || "",
                        slug: slugify(link.title) || "",
                        href: link.href || "",
                        status: 1,
                    }
                });
                if (more && link.content.length > 0) {
                    link.content.map(async (content) => {
                        const conetents = await db.content.create({
                            data: {
                                title: content.heading || "",
                                slug: slugify(content.heading) || "",
                                description: content.description || "",
                                moreId: more.id,
                            }
                        })

                    })
                }

                if (more && link.images.length > 0) {
                    link.images.map(async (image) => {
                        const images = await db.image.create({
                            data: {
                                type: "images",
                                url: image.url || "",
                                moreId: more.id,
                            }
                        })

                    })
                }
                return more;
            })
        );

        return storedMore;

    } catch (error) {
        console.error("Error restoring content:", error);
        throw error;
    }
}