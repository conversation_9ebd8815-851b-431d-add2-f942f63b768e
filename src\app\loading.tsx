import React from "react";

type Props = {};

const Loading = async (props: Props) => {
  return (
    <>
      <div className="h-screen w-screen bg-black opacity-75 backdrop-blur-sm flex items-center justify-center">
        <div className="flex justify-center items-center space-x-1 text-xl text-gray-100">
          <svg
            fill="none"
            className="w-36 h-36 animate-spin"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              clipRule="evenodd"
              d="M15.165 8.53a.5.5 0 01-.404.58A7 7 0 1023 16a.5.5 0 011 0 8 8 0 11-9.416-7.874.5.5 0 01.58.404z"
              fill="currentColor"
              fillRule="evenodd"
            />
          </svg>
          <div className="text-4xl">Loading ...</div>
        </div>
      </div>
    </>
  );
};

export default Loading;
