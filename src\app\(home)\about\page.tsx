"use server";
import React from "react";
import AboutUsHero from "@/components/main/about/Hero";
import AboutMain from "@/components/main/about/Main";
import { generateMetadata1 } from "@/lib/utils";

type Props = {};

export async function generateMetadata() {
  return generateMetadata1({
    page: "about",
  });
}

const About = (props: Props) => {
  return (
    <div className="h-full overflow-hidden">
      <AboutUsHero />
      <AboutMain />
    </div>
  );
};

export default About;
