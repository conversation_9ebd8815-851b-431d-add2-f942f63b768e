"use client";
import React from "react";
import Image from "next/image";
import DeleteForm from "@/components/admin/common/delete-form";
import moment from "moment";
import { deleteTag } from "@/actions/tag";
import toast from "react-hot-toast";
import Link from "next/link";
import ImagesUpload from "@/components/admin/common/images-upload";
import MarkDown from "@/components/common/mark-down";
import ImagePopup from "@/components/common/image-popup";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Edit } from "lucide-react";
import { useRouter } from "next/navigation";

type Props = {
  blog: any;
};

const BlogDetails = ({ blog }: Props) => {
  const router = useRouter();

  const handleDeleteTag = async (id: any) => {
    id = parseInt(id);
    const a = confirm("Are you sure you want to delete this tag?");
    if (!a) return;
    await deleteTag(id);
    toast.success("Tag Deleted Successfully!");
    router.refresh();
  };
  return (
    <div className="w-full p-10 max-w-7xl">
      <div className="flex flex-col gap-4">
        {blog.featureImage && (
          <div className="rounded-lg overflow-hidden mb-6">
            <Image
              priority
              width={1000}
              height={1000}
              src={
                blog?.featureImage ||
                "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/preloader%2Fplaceholder.png?alt=media&token=bf1e9987-d53b-46ad-b92f-c877c3d40982"
              }
              alt={blog?.title}
              className="w-full h-full md:max-h-[700px] object-fit shadow-2xl "
            />
          </div>
        )}

        <div className="w-full bg-black p-6 shadow rounded-lg mb-6">
          <div className="flex gap-6 items-center mb-6">
            {blog?.logo && (
              <Image
                src={
                  blog?.logo ||
                  "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/preloader%2Fplaceholder.png?alt=media&token=bf1e9987-d53b-46ad-b92f-c877c3d40982"
                }
                priority
                width={250}
                height={250}
                alt={blog?.title}
                className="w-16 h-16 rounded-full object-cover"
              />
            )}
            <h1 className="text-4xl font-bold capitalize">{blog?.title}</h1>
          </div>
          <div className="text-gray-300">
            {blog?.description ? (
              <MarkDown description={blog?.description} />
            ) : (
              "No description available."
            )}
          </div>
          <div className="mt-4 text-sm text-gray-200">
            {blog?.slug && (
              <span>
                <span className="mr-4">Slug:</span>
                <code className="bg-gray-600 p-1 rounded">{blog?.slug}</code>
              </span>
            )}

            <span className="ml-4">
              Status:
              <span
                className={
                  blog?.status === 1
                    ? "text-green-700 font-bold ml-4 bg-gray-200 p-1 rounded"
                    : "text-red-700 font-bold ml-4 bg-gray-200 p-1 rounded"
                }
              >
                {blog?.status === 1 ? "Active" : "Inactive"}
              </span>
            </span>
          </div>
          <p className="text-xs text-gray-400 mt-4">
            Created: {moment(blog?.createdAt).calendar()} | Updated:{" "}
            {moment(blog?.updatedAt).calendar()}
          </p>

          {blog?.tags && (
            <div className="mt-4">
              <div className="flex flex-wrap gap-2">
                {blog?.tags.map((tag: any) => (
                  <Badge
                    key={tag.id}
                    variant={"secondary"}
                    dismissible
                    onDismiss={() => handleDeleteTag(tag.id)}
                  >
                    {tag.text}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          <div className="text-xs text-gray-400 mt-4 flex flex-wrap gap-4">
            <Button
              asChild
              className="flex justify-center items-center"
              variant="secondary"
            >
              <Link href={`/admin/blog/${blog?.slug}/edit`}>
                <Edit width={20} height={20} />
                Edit Blog
              </Link>
            </Button>

            <DeleteForm
              id={blog?.id}
              text={blog?.slug}
              image={blog?.featureImage}
              page={"blog"}
            />

            <ImagesUpload id={blog?.id} page={"blog"} />
          </div>
        </div>
      </div>

      {blog?.images && (
        <div className="h-full w-full p-8">
          <h2 className="text-2xl font-semibold mb-4">
            Images Under {blog?.title}
          </h2>
          <ImagePopup images={blog?.images} />
        </div>
      )}
    </div>
  );
};

export default BlogDetails;
