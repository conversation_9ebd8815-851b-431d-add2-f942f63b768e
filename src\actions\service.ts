"use server";
import api from "@/lib/api";
import { db } from "@/lib/db";
import { createServiceQuery, deleteServiceQuery, getServiceQuery, getServicesQuery, updateServiceQuery } from "@/lib/queries"


export const createService = async (service: any) => {
    const services = await createServiceQuery(service);
    return services;
}

export const getService = async (slug: string) => {
    const service = await getServiceQuery(slug);
    return service;
}

export const getServices = async () => {
    const services = await getServicesQuery();
    return services;
}

export const updateService = async (service: any, id: any) => {
    const services = await updateServiceQuery(service, id);
    return services;
}

export const deleteService = async (id: any) => {
    const services = await deleteServiceQuery(id);
    return services;
}

export const restoreService = async () => {
    try {
        const data = await api.get(`/service`).then((res) => res.data);

        const storedServices = await Promise.all(
            data.service.map(async (service: any) => {
                return await db.service.create({
                    data: {
                        slug: service.slug || "",
                        title: service.title || "",
                        description: service.description || "",
                        featureImage: service.featureImage || "",
                        status: 1,
                        parent: {
                            connect: {
                                slug: service.parent.slug
                            }
                        }
                    }
                });
            })
        );

        console.log(storedServices);
        return storedServices;

    } catch (err) {
        console.log(err);
        return null;
    }
};
