"use server";
import React from "react";
import Link from "next/link";
import { getMoreLinks } from "@/actions/more";

type Props = {};

const MoreLinks = async (props: Props) => {
  let moreLinks = await getMoreLinks();

  moreLinks = moreLinks.slice(0, 10);

  return (
    <section className="py-8 bg-black/80 backdrop-blur-2xl">
      <h3 className="text-xl font-bold text-center mb-6">More Links</h3>
      <hr />
      <ul className="flex flex-wrap justify-center items-center gap-4 p-4 space-y-2">
        {moreLinks.map((link) => (
          <li key={link.id} className="py-4">
            <Link
              className="w-64 h-32 p-4 text-center text-white bg-blue-500 rounded-full hover:bg-blue-600"
              href={"/more" + link.href}
            >
              {link.title}
            </Link>
          </li>
        ))}
      </ul>
    </section>
  );
};

export default MoreLinks;
