"use server";
import React from "react";
import DataTable from "@/components/admin/common/data-table";
import { getSeos } from "@/actions/seo";
import ViewButtonTable from "@/components/admin/common/view-button-table";
import PageHeader from "@/components/admin/common/page-header";
import { seoFields } from "@/lib/staticdata";

type Props = {};

const SeoPage = async (props: Props) => {
  const seos: any = [];
  const data: any = await getSeos();

  data.map((item: any) => {
    seos.push({
      title: item.title,
      description: item.description,
      type: item.type,
      page: item.page,
      url: item.url,
      status: item.status,
      keywords: item.keywords,
      createdAt: item.createdAt,
      action: <ViewButtonTable page={"seo"} slug={item.page} />,
    });
  });

  return (
    <div className="p-8 flex flex-col justify-center items-center gap-y-8">
      <PageHeader page="seo" fields={seoFields} />
      <DataTable data={seos} />
    </div>
  );
};

export default SeoPage;
