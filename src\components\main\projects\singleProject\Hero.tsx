"use client";
import React, { useState } from "react";
import { ContainerScroll } from "@/components/ui/container-scroll-animation";
import Image from "next/image";

type Props = {
  project: any;
};

const ProjectDetailHero = ({ project }: Props) => {
  const [ishovered, setIsHovered] = useState(false);
  return (
    <>
      <ContainerScroll
        titleComponent={
          <>
            <h1 className="text-4xl font-semibold text-black dark:text-white">
              <span className="text-4xl uppercase lg:text-[6rem] font-bold mt-1 leading-none">
                {project.title}
              </span>
            </h1>
          </>
        }
      >
        <Image
          onMouseEnter={(e) => {
            setIsHovered(true);
          }}
          onMouseLeave={(e) => {
            setIsHovered(false);
          }}
          src={project?.featureImage || "/logo.png"}
          alt="hero"
          height={720}
          width={1400}
          priority
          className="mx-auto rounded-2xl object-cover h-full"
          style={{
            transition: !ishovered ? "all 4s ease" : "all 6s ease",
            objectPosition: !ishovered ? "center top" : "center bottom",
          }}
          draggable={false}
        />
      </ContainerScroll>
    </>
  );
};

export default ProjectDetailHero;
