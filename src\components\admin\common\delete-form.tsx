"use client";
import React, { useState } from "react";
import Modal from "./form-modal";
import toast from "react-hot-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import { deleteUTFiles } from "@/actions/delete-image";
import { deleteParent } from "@/actions/parent";
import { deleteProject } from "@/actions/project";
import { deleteService } from "@/actions/service";
import { deletePortfolio } from "@/actions/portfolio";
import { deleteMoreLink } from "@/actions/more";
import { deleteSeo } from "@/actions/seo";
import { deleteBlog } from "@/actions/blog";

type Props = {
  id: any;
  text: any;
  image: any;
  page: any;
};

const DeleteForm = ({ id, text, image, page }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [slug, setSlug] = useState("");

  const router = useRouter();

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (text === slug) {
      if (image) {
        const url = image;
        const parts = url.split("/");
        const uniqueCode = parts[parts.length - 1];
        await deleteUTFiles([uniqueCode]);
      }
      if (page === "parent") await deleteParent(id);
      if (page === "project") await deleteProject(id);
      if (page === "service") await deleteService(id);
      if (page === "portfolio") await deletePortfolio(id);
      if (page === "seo") await deleteSeo(id);
      if (page === "more") await deleteMoreLink(id);
      if (page === "blog") await deleteBlog(id);

      toast.success(`${page} Delete Successfully!`);
      router.push(`/admin/${page}`);
    } else {
      toast.error("Text Don't Match");
    }
  };

  return (
    <>
      <Button
        className="flex justify-center items-center"
        variant={"destructive"}
        onClick={openModal}
      >
        <Trash width={20} height={20} />
        Delete {page}
      </Button>
      <Modal isOpen={isModalOpen} onClose={closeModal} title={"Delete Parent"}>
        <div className="flex flex-col gap-6">
          <p>
            "Are you sure you want to delete this? This action cannot be
            undone."
          </p>
          <p>
            Please Type{" "}
            <span className="bg-red-500 p-1 rounded-sm"> {text}</span> to
            confirm please{" "}
          </p>
          <form onSubmit={handleSubmit} className="flex gap-4">
            <Input
              className="w-full"
              value={slug}
              onChange={(e) => setSlug(e.target.value)}
            />
            <Button className="capitalize">Delete {page}</Button>
          </form>
        </div>
      </Modal>
    </>
  );
};

export default DeleteForm;
