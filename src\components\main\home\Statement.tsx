"use client";
import { PhoneCallIcon, QuoteIcon } from "lucide-react";
import { motion } from "motion/react";
import React from "react";
import ContactModal from "../common/contact-modal";

type Props = {};

const HomeStatement = (props: Props) => {
  return (
    <section
      style={{
        backgroundImage: `url(
          "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/homepage%2Fstatements%2Fcontact-us-image.webp?alt=media&token=680d3727-01a8-4df9-912d-871b5de821da")`,
      }}
      className=" h-[50vh] lg:h-screen  flex justify-center items-center"
    >
      <div className="h-[50vh] lg:h-screen w-full text-gray-200 bg-black bg-clip-padding backdrop-filter backdrop-blur-none bg-opacity-80 backdrop-saturate-70 backdrop-contrast-100 flex items-center justify-center">
        <div className="flex flex-col justify-center items-center gap-y-8">
          <motion.div
            animate={{
              scale: [1, 1.8, 1],
              opacity: [1, 0.7, 1],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse",
            }}
            className="mb-8"
          >
            <PhoneCallIcon size={64} />
          </motion.div>

          <h3 className="text-xl lg:text-6xl font-bold inline-flex items-center justify-center">
            <QuoteIcon size={32} />
            Contact us today to get started!
            <QuoteIcon size={32} />
          </h3>

          <div>
            <ContactModal />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HomeStatement;
