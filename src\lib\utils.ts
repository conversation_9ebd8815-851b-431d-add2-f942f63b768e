import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { db } from "./db";
import { Metadata } from "next";
import { getParents } from "@/actions/parent";


export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function convertToHTML(text: any) {
  // Replace ## with <h2> and # with <h1>
  const replacedText = text
    .replace(/#{2}\s(.+?)\n/g, '<h2 class="text-xl font-bold mt-2">$1</h2>\n')
    .replace(/#{1}\s(.+?)\n/g, '<h2 class="text-xl font-bold mt-2">$1</h2>\n');
  // Convert text to HTML
  const html = replacedText
    .replace(/\n\n/g, '</p>\n<div class="my-2"></div>\n')
    .replace(/\n/g, " ");
  return html;
}

const blockKeywords = [
  "Sinelogix",
  "Sinelogix Technology Pvt. Ltd",
  "<EMAIL>",
  "itsdennismiller",
  "8980898451",
  "9979553686",
  "09979553686",
  "Hello, I hope you are doing well. Sinelogix Technology Pvt. Ltd is a Website Design and Development company based out in India. We are working into web development from last 10 years supporting on various kind of technologies like Magento, WordPress, Laravel, Codeigniter, PHP, HTML etc. Here is our cost: 1. Sr.PHP developer with 3-5 years of experience : 60000 INR per month 2. Sr. Laravel developer with more than 3-5 years of experience : 65000 INR per month 3. Sr. WordPress/Woo-Commerce developer with 3-5 years of experience: 55000 INR per month 4. Magento developer with 3-5 years of experience: 65000 INR per month 5. Android Developer : 1,20,000 INR per month 6. React Developer : 90000 INR per month. 7. Flutter Developer : 1,30,000 INR Per Month 8. IOS Developer with 2-3 Years of Experience : 90000 INR per month 9. SEO Engineer with 3-5 Years of Experience : 35000 INR per month. 10. QA Automation Engineer with 3-5 Years of Experience : 65000 INR Per Month 11.Shopify Developer: 3-5 years of experience: 70000 INR/Month 12.Java developer with 3+ Years of experience: 1,60,000 INR/per month. Our Payment Term: There is no advance payment, we can sign monthly contracts and you can pay every month end. If you are looking for any help please email me at mailto:<EMAIL> or whatsapp me at +91 8980898451 / +91 9979553686 Our Skype id is: itsdennismiller",
  "<EMAIL>",
];

export function containsKeywords(str: string) {
  const words = str.toLowerCase().split(/\s+/);

  for (let i = 0; i < blockKeywords.length; i++) {
    const keyword = blockKeywords[i].toLowerCase();

    if (words.includes(keyword)) {
      return true;
    }
    if (str.toLowerCase().includes(keyword)) {
      return true;
    }
  }
  return false;
}

export const convertStatusToValue = (status: number) => {
  switch (status) {
    case 0:
      return "Closed";
    case 1:
      return "Upcoming";
    case 2:
      return "Live";
    default:
      return "Unknown";
  }
};

export const convertStatusToColor = (status: number) => {
  switch (status) {
    case 0:
      return "bg-red-500/50 dark:bg-red-500/50 text-pink-900";
    case 1:
      return "bg-indigo-500/50 dark:bg-indigo-500/50 text-pink-900";
    case 2:
      return "bg-green-500/50 dark:bg-green-500/50 text-pink-900";
    default:
      return "bg-yellow-500/50 dark:bg-yellow-500/50 text-pink-900";
  }
};

export async function generateMetadata1(options: {
  page: string;
}): Promise<Metadata> {
  const data = await db.seo.findFirst({ where: { page: options.page } });
  const defaultMetadata: Metadata = {
    title: data?.title || "Best Website Design Company",
    description: data?.description || "Tech Fossil is a leading web design and development company serving clients in India, USA, and UK. We offer creative, responsive, and custom website solutions for businesses of all sizes.",
    keywords: data?.keywords,
    authors: [{ name: "The Tech Fossil", url: "https://www.thetechfossil.com/" }],
    alternates: {
      canonical: "https://www.thetechfossil.com/" + data?.url,
    },
    openGraph: {
      title: data?.title || "Best Website Design Company",
      description: data?.description || "Tech Fossil is a leading web design and development company serving clients in India, USA, and UK. We offer creative, responsive, and custom website solutions for businesses of all sizes.",
      url: "https://www.thetechfossil.com/" + data?.url,
      siteName: "The Tech Fossil",
      images: "https://www.thetechfossil.com/favicon.ico",
      locale: "en_US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: data?.title || "Best Website Design Company",
      description: data?.description || "Tech Fossil is a leading web design and development company serving clients in India, USA, and UK. We offer creative, responsive, and custom website solutions for businesses of all sizes.",
      images: "https://www.thetechfossil.com/favicon.ico",
    },
  };

  // Merge default metadata with provided options
  const mergedMetadata = {
    ...defaultMetadata,
    ...options,
  };

  return mergedMetadata;
}

export const parentOptionFormat = async () => {
  const data: any = []

  const parents = await getParents()

  await parents.map((parent) => {
    data.push({
      value: parseInt(parent.id as any),
      label: parent.title
    })
  })

  return data
}

export function getRandomClass(index: number, totalCards: number): string {
  const classes = [
    "col-span-1",
    "md:col-span-2",
    "md:col-span-3",
    "md:col-span-4",
    "md:col-span-5",
  ];

  // Ensure the first card spans two columns for larger screens
  if (index === 0) {
    return "md:col-span-2";
  }

  // Randomly select a class
  const randomIndex = Math.floor(Math.random() * classes.length);
  return classes[randomIndex];
}
