//"https://blenderartists.org/uploads/default/original/4X/5/0/2/5029a48052f6f91a399a41dcd1004adf854b17ea.jpeg?height=1024&width=2048"
"use client";

import React, { useRef, useEffect } from "react";
import * as THREE from "three";

const Globe: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!mountRef.current) return;

    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x000000); // Make the background transparent
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      100
    );
    camera.position.set(15, 10, 20); // Initial camera position
    camera.lookAt(0, 0, 0);

    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    mountRef.current.appendChild(renderer.domElement);

    // Create Earth

    // Load textures
    const dayTexture = new THREE.TextureLoader().load("/005.jpg");
    const nightTexture = new THREE.TextureLoader().load("/003.jpg");
    const galaxyTexture = new THREE.TextureLoader().load("/sky.png"); // Load sky texture

    // Create materials
    const dayMaterial = new THREE.MeshPhongMaterial({ map: dayTexture });
    const nightMaterial = new THREE.MeshPhongMaterial({ map: nightTexture });

    // Create a single sphere geometry
    const sphereGeometry = new THREE.SphereGeometry(10, 64, 64);

    // Create a material that blends day and night textures based on longitude
    const blendedMaterial = new THREE.ShaderMaterial({
      uniforms: {
        dayTexture: { value: dayTexture },
        nightTexture: { value: nightTexture },
        longitude: { value: 0 }, // Initial longitude for day/night boundary
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform sampler2D dayTexture;
        uniform sampler2D nightTexture;
        uniform float longitude;

        varying vec2 vUv;

        void main() {
          float x = vUv.x * 2.0 - 1.0; // Normalize x to [-1, 1]
          float angle = atan(x, vUv.y); // Calculate longitude angle

          float blendFactor = smoothstep(-longitude - 0.1, -longitude + 0.1, angle); 
          blendFactor = clamp(blendFactor, 0.0, 1.0);

          vec4 dayColor = texture2D(dayTexture, vUv);
          vec4 nightColor = texture2D(nightTexture, vUv);

          gl_FragColor = mix(dayColor, nightColor, blendFactor);
        }
      `,
    });

    const sphere = new THREE.Mesh(sphereGeometry, blendedMaterial);
    scene.add(sphere);

    // Create a galaxy background
    const galaxyGeometry = new THREE.SphereGeometry(50, 32, 32); // Larger sphere for galaxy
    const galaxyMaterial = new THREE.MeshBasicMaterial({
      map: galaxyTexture,
      side: THREE.BackSide,
    });
    const galaxy = new THREE.Mesh(galaxyGeometry, galaxyMaterial);
    scene.add(galaxy);

    // Add ambient light
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);

    // Add directional light (sun-like)
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 3, 5);
    scene.add(directionalLight);

    camera.position.set(15, 10, 15); // Initial camera position
    camera.lookAt(0, 0, 0); // Look at the center of the Earth

    const animate = () => {
      requestAnimationFrame(animate);
      sphere.rotation.y += 0.009;
      renderer.render(scene, camera);
    };

    animate();

    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
      mountRef.current?.removeChild(renderer.domElement);
    };
  }, []);

  return (
    <div ref={mountRef} className="absolute inset-0 -z-10" aria-hidden="true" />
  );
};

export default Globe;
