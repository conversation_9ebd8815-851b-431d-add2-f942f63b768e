"use client";
import React from "react";
import Image from "next/image";
import { Blog } from "@prisma/client";
import { BentoGrid, BentoGridItem } from "../../ui/bento-grid";
import { motion } from "framer-motion";

type Props = {
  posts?: Blog[];
};

export const BlogGrid = ({ posts }: Props) => {
  return (
    <>
      {posts && posts.length > 0 ? (
        <BentoGrid className="max-w-4xl h-full mx-auto pb-32">
          {posts?.map((item: Blog, i: number) => (
            <BentoGridItem
              key={i}
              title={item.title}
              description={
                (item.description.substring(0, 250) as string) + "..."
              }
              header={
                <Image
                  priority
                  src={
                    item.featureImage
                      ? item.featureImage
                      : "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/preloader%2Fplaceholder.png?alt=media&token=bf1e9987-d53b-46ad-b92f-c877c3d40982"
                  }
                  alt={item.title || "Blog Post"}
                  width={0}
                  height={0}
                  sizes="100vw"
                  className="flex flex-1 w-full  min-h-[6rem] rounded-xl bg-gradient-to-br from-neutral-200 dark:from-neutral-900 dark:to-neutral-800 to-neutral-100w-full h-full object-cover"
                />
              }
              icon={null}
              slug={item.slug}
              className={i % 5 === 0 ? "md:col-span-2" : ""}
            />
          ))}
        </BentoGrid>
      ) : (
        <div className="w-full md:min-w-screen h-full flex justify-center items-center text-2xl">
          <div className="flex items-center justify-center h-64">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center"
            >
              <motion.h2
                className="text-2xl font-bold text-gray-200 mb-2"
                initial={{ scale: 0.9 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 120 }}
              >
                No blog posted yet
              </motion.h2>
              <motion.p
                className="text-gray-100"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4, duration: 0.5 }}
              >
                Check back soon for new content!
              </motion.p>
            </motion.div>
          </div>
        </div>
      )}
    </>
  );
};
