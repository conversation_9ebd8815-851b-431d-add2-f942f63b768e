import { db } from "@/lib/db";
import { Seo } from "@prisma/client";
import { MetadataRoute } from "next";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
    // Await the Promise to resolve the array
    const seos: Seo[] = await db.seo.findMany();

    // Ensure no null `url` entries are included
    const seoEntries: MetadataRoute.Sitemap = seos
        .filter((seo) => seo.url)
        .map((seo) => ({
            url: `${process.env.NEXT_PUBLIC_BASE_URL}${seo.url}`,
            priority: 1,
            changeFrequency: "daily",
            lastModified: seo.updatedAt
        }));

    // Return the sitemap
    return [
        ...seoEntries,
    ];
}











