"use server";
import DetailPage from "@/components/admin/common/detail-page";
import { moreLinksField } from "@/lib/staticdata";
import { getMoreLink } from "@/actions/more";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};
const MoreLinkSlugPage = async ({ params }: Props) => {
  const { slug } = await params;
  const more = await getMoreLink(slug);

  return (
    <>
      <DetailPage
        data={more}
        page="more"
        fields={moreLinksField}
        isContnent={true}
      />
    </>
  );
};

export default MoreLinkSlugPage;
