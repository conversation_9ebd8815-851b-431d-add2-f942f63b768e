"use client";
import React from "react";
import { HeroHighlight, High<PERSON> } from "../../ui/hero-highlight";
import { motion } from "framer-motion";
import { TextGenerateEffect } from "../../ui/text-generate-effect";
import ContactModal from "../common/contact-modal";

type Props = {};

const Hero = (props: Props) => {
  const desc =
    "Our design and development process is built on a foundation of strategic thinking, meticulous attention to detail, and a deep understanding of user behavior. We collaborate closely with you to create captivating designs, intuitive user experiences, and cutting-edge features that captivate your visitors and keep them coming back.";
  return (
    <>
      <HeroHighlight>
        <section className="min-h-screen flex justify-center items-center">
          <div className="grid max-w-screen-xl px-4 py-48 mx-auto lg:gap-8 xl:gap-0 lg:py-32 lg:grid-cols-12 lg:mt-20">
            <div className="text-black mr-auto place-self-center lg:col-span-7">
              <motion.h1
                initial={{
                  opacity: 0,
                  y: 20,
                }}
                animate={{
                  opacity: 1,
                  y: [20, -5, 0],
                }}
                transition={{
                  duration: 0.5,
                  ease: [0.4, 0.0, 0.2, 1],
                }}
                className="max-w-2xl text-2xl md:text-3xl lg:text-4xl font-bold text-neutral-700 dark:text-white leading-relaxed lg:leading-snug"
              >
                Elevate Your Online Presence with
                <br />
                <Highlight className="text-black dark:text-white">
                  Our Website Design & Development Services
                </Highlight>
              </motion.h1>
              {/* <p className="mt-4 max-w-2xl mb-6 font-d text-gray-200 lg:mb-8 lg:text-lg lg:text-xl dark:text-gray-200"> */}
              <TextGenerateEffect words={desc} className="mb-6" />
              {/* </p> */}
              <ContactModal />
            </div>
            <div className="hidden lg:mt-0 lg:col-span-5 lg:flex">
              <img
                src="https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/homepage%2FFirst-Fold%2Fundraw_building_websites_i78t.svg?alt=media&token=e3240d50-7591-48c7-9665-5587f3f5d40b"
                alt="mockup"
              />
            </div>
          </div>
        </section>
      </HeroHighlight>
    </>
  );
};

export default Hero;
