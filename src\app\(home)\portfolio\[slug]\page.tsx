"use server";
import React from "react";
import PortfolioSingleCard from "@/components/main/portfolio/single/Card";
import PortfolioSingleHero from "@/components/main/portfolio/single/Hero";
import { getPortfolio } from "@/actions/portfolio";
import { generateMetadata1 } from "@/lib/utils";

type Props = {
  params: Promise<{ slug: string }>;
};

export async function generateMetadata({ params }: Props) {
  const { slug } = await params;
  return generateMetadata1({
    page: slug,
  });
}

const SingePortfolio = async ({ params }: Props) => {
  const { slug } = await params;
  const portfolio = await getPortfolio(slug);

  return (
    <>
      <div className="p-10 flex flex-col overflow-hidden">
        {portfolio && <PortfolioSingleHero portfolio={portfolio} />}
        <PortfolioSingleCard portfolio={portfolio} />
      </div>
    </>
  );
};

export default SingePortfolio;
