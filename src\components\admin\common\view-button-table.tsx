import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Eye } from "lucide-react";
import Link from "next/link";

type Props = {
  page: string;
  slug: string;
};

const ViewButtonTable = ({ page, slug }: Props) => {
  return (
    <Button asChild variant="default" size="default">
      <Link href={`/admin/${page}/${slug}`}>
        <Eye width={20} height={20} /> View
      </Link>
    </Button>
  );
};

export default ViewButtonTable;
