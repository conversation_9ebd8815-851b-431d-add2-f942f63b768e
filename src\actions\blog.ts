"use server";
import api from "@/lib/api";
import { db } from "@/lib/db";
import { createBlogQuery, deleteBlogQuery, getBlogQuery, getBlogsQuery, updateBlogQuery } from "@/lib/queries";


export const createBlog = async (blog: any) => {
    const data = await createBlogQuery(blog);
    return data
}

export const updateBlog = async (blog: any, id: any) => {
    const data = await updateBlogQuery(blog, id);
    return data
}

export const getBlog = async (slug: string) => {
    const data = await getBlogQuery(slug);

    return data;
}

export const getBlogs = async () => {
    const data = await getBlogsQuery();

    return data
}

export const deleteBlog = async (id: any) => {
    const data = await deleteBlogQuery(id)
    return data
}


export const restoreBlog = async () => {

    const oldBlogs: any = []
    const data = await api.get(`/blog`).then((res) => res.data);
    await data.blog.forEach(async (blog: any) => {
        await oldBlogs.push({
            title: blog.title || "",
            slug: blog.slug || "",
            description: blog.description || "",
            featureImage: blog.featureImage || "",
            status: 1,
        })
    })

    const data1 = await db.blog.createMany({ data: oldBlogs });
    return data1
}