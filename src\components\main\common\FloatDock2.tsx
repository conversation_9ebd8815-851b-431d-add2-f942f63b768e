"use client";
import { useState, useRef, useEffect } from "react";
import {
  Facebook,
  Twitter,
  Instagram,
  Mail,
  Phone,
  X,
  Dock,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import {
  IconBrandLinkedin,
  IconBrandWhatsapp,
  IconBrandX,
} from "@tabler/icons-react";

type DockItem = {
  icon: React.ElementType;
  label: string;
  href: string;
};

const dockItems: DockItem[] = [
  {
    icon: Facebook,
    label: "Facebook",
    href: "https://facebook.com/thetechfossil",
  },
  {
    icon: IconBrandX,
    label: "X.com",
    href: "https://x.com/thetechfossil",
  },
  {
    icon: Instagram,
    label: "Instagram",
    href: "https://instagram.com/thetechfossil",
  },
  {
    icon: IconBrandLinkedin,
    label: "LinkedIn",
    href: "https://www.linkedin.com/company/thetechfossil",
  },
  { icon: Mail, label: "Email", href: "mailto:<EMAIL>" },
  { icon: Phone, label: "Phone", href: "tel:+919718791401" },
  // { icon: MapPin, label: "Location", href: "https://maps.google.com" },
  {
    icon: IconBrandWhatsapp,
    label: "WhatsApp",
    href: "https://wa.me/+919718791401",
  },
];

const FloatDock2 = () => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [isDockVisible, setIsDockVisible] = useState(false);
  const dockRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleMouseLeave = () => setHoveredIndex(null);
    const dockElement = dockRef.current;
    if (dockElement) {
      dockElement.addEventListener("mouseleave", handleMouseLeave);
      return () => {
        dockElement.removeEventListener("mouseleave", handleMouseLeave);
      };
    }
  }, []);

  const toggleDock = () => {
    setIsDockVisible(!isDockVisible);
  };

  return (
    <>
      <AnimatePresence>
        {isDockVisible && (
          <motion.div
            ref={dockRef}
            onMouseLeave={() => setHoveredIndex(null)}
            className="z-0 min-w-[350px] h-auto lg:h-[65px] fixed bottom-24 lg:bottom-14 left-5 lg:left-1/3 transform -translate-x-1/2 flex items-end justify-center space-x-2 p-2 rounded-2xl bg-white bg-opacity-20 backdrop-blur-md shadow-lg"
            // style={{ minWidth: "500px", height: "65px" }}
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            transition={{ duration: 0.3 }}
          >
            <div className="grid grid-cols-3 p-4 lg:p-0 gap-6 md:gap-4 lg:gap-4 md:flex md:items-end">
              {dockItems.map((item, index) => (
                <DockIcon
                  key={item.label}
                  item={item}
                  isHovered={index === hoveredIndex}
                  onHover={() => setHoveredIndex(index)}
                />
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      <motion.button
        className="z-0 fixed bottom-14 right-4 bg-white text-black bg-opacity-50 rounded-full p-3 shadow-lg"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={toggleDock}
        aria-label={isDockVisible ? "Hide dock" : "Show dock"}
      >
        {isDockVisible ? <X size={24} /> : <Dock size={24} />}
      </motion.button>
    </>
  );
};

const DockIcon = ({
  item,
  isHovered,
  onHover,
}: {
  item: DockItem;
  isHovered: boolean;
  onHover: () => void;
}) => {
  const baseSize = 50;
  const hoverSize = 65;
  const popOutDistance = 20;
  const size = isHovered ? hoverSize : baseSize;

  return (
    <div className="flex flex-col items-center">
      <a
        href={item.href}
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center justify-center rounded-full bg-black shadow-2xl text-white transition-all duration-200"
        style={{
          width: `${size}px`,
          height: `${size}px`,
          transform: `translateY(${isHovered ? -popOutDistance : 0}px)`,
        }}
        onMouseEnter={onHover}
        aria-label={item.label}
      >
        <item.icon size={size * 0.6} />
      </a>
      {isHovered && (
        <div className="absolute bottom-full mb-4 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-100 transition-opacity duration-200">
          {item.label}
        </div>
      )}
    </div>
  );
};

export default FloatDock2;
