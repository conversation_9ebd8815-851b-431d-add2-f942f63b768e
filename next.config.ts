import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'firebasestorage.googleapis.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: "assets.aceternity.com",
        pathname: "/**",
      },
      {
        protocol: 'https',
        hostname: "aceternity.com",
        pathname: "/**",
      },
      {
        protocol: 'https',
        hostname: "images.unsplash.com",
        pathname: "/**",
      },
      {
        protocol: 'https',
        hostname: "placehold.co",
        pathname: "/**",
      },
      {
        protocol: 'https',
        hostname: "memeart.com.au",
        pathname: "/**",
      },
      {
        protocol: 'https',
        hostname: "utfs.io",
        pathname: "/**",
      }
    ],
  },
};

export default nextConfig;
