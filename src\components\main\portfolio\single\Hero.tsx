import React from "react";
import { MacbookScroll } from "@/components/ui/macbook-scroll";
import Image from "next/image";
import Link from "next/link";

type Props = {
  portfolio: any;
};

const PortfolioSingleHero = ({ portfolio }: Props) => {
  return (
    <div className="overflow-hidden">
      <MacbookScroll
        title={<span className="text-7xl capitalize">{portfolio.title}</span>}
        badge={
          <Link href={portfolio.link}>
            <Image
              priority
              src={
                portfolio.logo ||
                "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/logo%2Fttfnl.png?alt=media&token=a058c8d9-33db-4686-a0dc-6a4ed4932809"
              }
              height={100}
              width={100}
              alt="macbook"
            />
          </Link>
        }
        src={portfolio.featureImage || ""}
        showGradient={false}
      />
    </div>
  );
};
export default PortfolioSingleHero;
