"use client";
import { cn } from "@/lib/utils";
import HTMLReactParser from "html-react-parser/lib/index";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

export const BentoGrid = ({
  className,
  children,
}: {
  className?: string;
  children?: React.ReactNode;
}) => {
  return (
    <div
      className={cn(
        "grid h-full lg:auto-rows-[30rem] grid-cols-1 lg:grid-cols-3 gap-4 max-w-7xl mx-auto ",
        className
      )}
    >
      {children}
    </div>
  );
};

export const BentoGridItem = ({
  className,
  title,
  description,
  header,
  icon,
  slug,
}: {
  className?: string;
  title?: string | React.ReactNode;
  description?: string | React.ReactNode;
  header?: string | React.ReactNode;
  icon?: React.ReactNode;
  slug: string;
}) => {
  return (
    <div
      className={cn(
        "row-span-1 rounded-xl group/bento hover:shadow-xl transition duration-200 shadow-input dark:shadow-none p-4 dark:bg-black/50 drop-blur-xl dark:border-white/[0.2] bg-white border border-transparent justify-between flex flex-col space-y-4",
        className
      )}
    >
      {header}
      <div className="group-hover/bento:translate-x-2 transition duration-200">
        {icon}
        <div className="font-sans font-bold text-neutral-600 dark:text-neutral-200 mb-2 mt-2">
          {title}
        </div>
        <div className="font-sans font-normal text-neutral-600 text-xs dark:text-neutral-300">
          {HTMLReactParser(description as string)}
        </div>
        <div className=" py-4 font-sans font-normal text-neutral-600 text-xs dark:text-neutral-300">
          <Link
            className="w-2/4 p-4 rounded-md justify-center text-gray-900 text-md font-semibold bg-white inline-flex gap-2"
            href={"/blogs/" + slug}
          >
            <span>Read More</span>
            <ArrowRight size={16} />
          </Link>
        </div>
      </div>
    </div>
  );
};
