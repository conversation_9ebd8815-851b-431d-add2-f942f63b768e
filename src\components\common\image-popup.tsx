"use client";
import React, { useState } from "react";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import Image from "next/image";

type Props = {
  images: any;
};

const ImagePopup = ({ images }: Props) => {
  const getGridCols = (imageCount: number) => {
    if (imageCount === 1) return "grid-cols-1";
    if (imageCount === 2) return "grid-cols-1 sm:grid-cols-2";
    if (imageCount === 3) return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3";
    if (imageCount === 4) return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4";
    return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4";
  };

  const [selectedImage, setSelectedImage] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="p-0 h-full w-full overflow-hidden bg-transparent border-none">
          <DialogTitle className="hidden" />
          <Image
            src={images[selectedImage]?.url || "/placeholder.svg"}
            alt="Main Image"
            width={800}
            height={600}
            className="w-full h-full rounded-lg aspect-auto object-contain overflow-hidden bg-transparent"
            style={{ aspectRatio: "800/600" }}
          />
        </DialogContent>
      </Dialog>
      <div className={`grid ${getGridCols(images?.length)} gap-4`}>
        {images?.map((image: any) => (
          <div key={image.id} className="hover:cursor-pointer">
            <Image
              onClick={() => {
                setSelectedImage(images.indexOf(image));
                setIsModalOpen(true);
              }}
              priority
              prefix="/favicon.ico"
              src={image.url}
              alt={"ttf"}
              quality={100}
              width={800}
              height={800}
              className="rounded-lg shadow-md object-cover w-full h-[80vh]"
            />
          </div>
        ))}
      </div>
    </>
  );
};

export default ImagePopup;
