"use server";
import { getProjects } from "@/actions/project";
import ProjectHero from "@/components/main/projects/Hero";
import ProjectMain from "@/components/main/projects/Main";
import { generateMetadata1 } from "@/lib/utils";
import React from "react";

type Props = {};

export async function generateMetadata() {
  return generateMetadata1({
    page: "projects",
  });
}

const Projects = async (props: Props) => {
  const projects = await getProjects();
  return (
    <div>
      <ProjectHero />
      <ProjectMain projects={projects} />
    </div>
  );
};

export default Projects;
