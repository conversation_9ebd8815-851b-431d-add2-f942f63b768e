"use client";
import React from "react";
import { stats } from "../../../lib/staticdata";
import { motion } from "motion/react";

type Props = {};

const HomeStats = (props: Props) => {
  return (
    <section
      className="bg-cover bg-center bg-fixed bg-no-repeat h-screen lg:h-screen"
      style={{
        backgroundImage:
          "url('https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/homepage%2Fstats%2Fstats-bg.jpg?alt=media&token=221de7ca-be4d-479e-abed-6c91444927ce')",
      }}
    >
      <div className="w-screen h-screen bg-black/80">
        <div className="w-full lg:w-10/12 py-16 px-4 mx-auto h-full flex flex-col justify-center ">
          {/* Heading */}
          <div className="text-left mb-12">
            <div className="">
              <h5 className="text-sm uppercase text-blue-500">
                Our Achievements
              </h5>
              <h2 className="text-4xl font-bold text-gray-100">
                We Deliver
                <br />
                <strong className="text-blue-500">Excellent</strong> Services
              </h2>
            </div>
          </div>

          {/* Stats Items */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 text-center">
            {stats &&
              stats.map((stat) => (
                <motion.div
                  animate={{ scale: [0.9, 1, 0.9] }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    repeatType: "reverse",
                  }}
                  key={stat.id}
                  className="flex flex-col gap-y-2 bg-white bg-opacity-90 backdrop-blur-sm shadow-lg rounded-lg p-8"
                >
                  <stat.icon className="w-12 h-12 text-blue-500 mx-auto" />
                  <h3 className="text-3xl font-bold text-gray-800">
                    {stat.number}
                  </h3>
                  <p className="text-gray-600">{stat.title}</p>
                </motion.div>
              ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default HomeStats;
