import React from "react";
import Image from "next/image";
import { WobbleCard } from "@/components/ui/wobble-card";
import Link from "next/link";
import { convertStatusToValue } from "@/lib/utils";
import { ExternalLink } from "lucide-react";

type Props = {
  portfolio: any;
};

const PortfolioSingleCard = ({ portfolio }: Props) => {
  return (
    <div className="overflow-hidden grid grid-cols-1 lg:grid-cols-3 gap-4 max-w-7xl mx-auto w-full">
      <WobbleCard containerClassName="col-span-1 lg:col-span-3 bg-blue-900 min-h-[500px] lg:min-h-[600px] xl:min-h-[300px]">
        <div className="max-w-3xl">
          <span className="capitalize px-3 py-1.5 text-md shadow-lg shadow-blue-500/40 text-blue-800 bg-blue-100 rounded-full">
            {convertStatusToValue(portfolio.status)}
          </span>
          <h2 className="mt-3 max-w-sm lg:max-w-lg  text-left text-balance text-base md:text-xl lg:text-3xl font-semibold tracking-[-0.015em] text-white">
            {portfolio.title}
          </h2>
          <p className="mt-1 max-w-sm lg:max-w-lg  text-left text-balance text-base lg:text-md lg:text-xl font-semibold tracking-[-0.015em] text-white/80">
            {portfolio.type}
          </p>
          <p className="mt-4 max-w-[48rem] text-left  text-base/6 text-neutral-200">
            {portfolio.description}
          </p>
          <p className="flex flex-wrap gap-4 mt-4">
            {portfolio.tags &&
              portfolio.tags?.map((tag: any, index: number) => (
                <span
                  key={index}
                  className="capitalize px-3 py-1.5 text-md shadow-lg shadow-cyan-500/40 text-cyan-800 bg-cyan-100 rounded-full"
                >
                  {tag}
                </span>
              ))}
          </p>
        </div>

        <Image
          priority
          src={
            portfolio.logo ||
            "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/logo%2Fttfnl.png?alt=media&token=a058c8d9-33db-4686-a0dc-6a4ed4932809"
          }
          width={500}
          height={500}
          alt="linear demo image"
          className="absolute -right-1 md:-right-[1%] lg:-right-[1%] -bottom-4 object-contain rounded-2xl"
        />
      </WobbleCard>
      <div className="fixed bottom-4 right-4 z-50 inline-flex items-center justify-center w-48 h-48">
        <Link
          href={portfolio.link}
          target="_blank"
          className="mt-4 w-full h-12 flex shadow-2xl justify-center items-center bg-trasparent text-white dark:bg-white dark:text-black text-sm px-2 py-1 rounded-md border border-black"
        >
          Visit Website
          <ExternalLink size={16} strokeWidth={3} className="ml-4" />
        </Link>
      </div>
    </div>
  );
};

export default PortfolioSingleCard;
