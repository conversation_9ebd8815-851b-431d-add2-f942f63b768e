"use client";
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";

export interface CardData {
  id: number;
  title?: string;
  description?: string;
  imageUrl: string;
  className: string;
}

export const LayoutGrid = ({ cards }: { cards: CardData[] }) => {
  const [selected, setSelected] = useState<CardData | null>(null);
  const [lastSelected, setLastSelected] = useState<CardData | null>(null);

  const handleClick = (card: CardData) => {
    setLastSelected(selected);
    setSelected(card);
  };

  const handleOutsideClick = () => {
    setLastSelected(selected);
    setSelected(null);
  };

  return (
    <>
      <div className="w-full h-full p-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4  mx-auto relative">
        {cards.map((card) => (
          <div key={card.id} className={cn(card.className, "h-96")}>
            <motion.div
              onClick={() => handleClick(card)}
              className={cn(
                "relative overflow-hidden",
                lastSelected?.id === card.id
                  ? "z-40 bg-white rounded-xl h-full w-full"
                  : "bg-white rounded-xl h-full w-full"
              )}
              layoutId={`card-${card.id}`}
            >
              <ImageComponent card={card} />
            </motion.div>
          </div>
        ))}
      </div>
      <AnimatePresence>
        {selected && (
          <SelectedCard
            selected={selected}
            handleOutsideClick={handleOutsideClick}
          />
        )}
      </AnimatePresence>
    </>
  );
};

const ImageComponent = ({ card }: { card: CardData }) => {
  return (
    <motion.img
      layoutId={`image-${card.id}-image`}
      src={card.imageUrl}
      alt="Card image"
      className="object-cover w-full h-full absolute inset-0 transition duration-200"
    />
  );
};

const SelectedCard = ({
  selected,
  handleOutsideClick,
}: {
  selected: CardData;
  handleOutsideClick: () => void;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 w-full h-full bg-black bg-opacity-80 z-50 flex items-center justify-center"
      onClick={handleOutsideClick}
    >
      <motion.div
        layoutId={`card-${selected.id}`}
        className="relative w-[80vw] h-[80vh] max-w-4xl max-h-[600px]"
        onClick={(e) => e.stopPropagation()}
      >
        <motion.img
          layoutId={`image-${selected.id}-image`}
          src={selected.imageUrl}
          alt="Selected card image"
          className="w-full h-full object-fit rounded-lg"
        />
      </motion.div>
    </motion.div>
  );
};
