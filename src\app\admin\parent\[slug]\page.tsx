"use server";
import DetailPage from "@/components/admin/common/detail-page";
import { parentFields } from "@/lib/staticdata";
import { getParent } from "@/actions/parent";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};
const ParentSlugPage = async ({ params }: Props) => {
  const { slug } = await params;
  const parent = await getParent(slug);

  return (
    <>
      <DetailPage
        data={parent}
        page="parent"
        fields={parentFields}
        isImages={false}
      />
    </>
  );
};

export default ParentSlugPage;
