"use client";

import { motion } from "framer-motion";
import Link from "next/link";

export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen ">
      <div className="w-full max-w-md">
        <svg viewBox="0 0 400 200" className="w-full">
          {/* Road */}
          <motion.path
            d="M0 150 L400 150"
            stroke="#333"
            strokeWidth="4"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 2 }}
          />

          {/* Dead End Sign */}
          <motion.g
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2, duration: 0.5 }}
          >
            <rect x="340" y="90" width="40" height="60" fill="#f87171" />
            <text
              x="360"
              y="120"
              textAnchor="middle"
              fill="white"
              fontSize="10"
              transform="rotate(-90 360 120)"
            >
              DEAD END
            </text>
            <line
              x1="360"
              y1="150"
              x2="360"
              y2="190"
              stroke="#f87171"
              strokeWidth="4"
            />
          </motion.g>

          {/* Car */}
          <motion.g
            initial={{ x: -100 }}
            animate={{ x: 250 }}
            transition={{ duration: 2, ease: "easeOut" }}
          >
            <rect x="0" y="125" width="60" height="20" fill="#3b82f6" />
            <rect x="10" y="115" width="30" height="10" fill="#60a5fa" />
            <circle cx="15" cy="145" r="8" fill="#1f2937" />
            <circle cx="45" cy="145" r="8" fill="#1f2937" />
          </motion.g>
        </svg>
      </div>

      <motion.h1
        className="text-6xl font-bold text-gray-600 mt-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2.5, duration: 0.5 }}
      >
        404
      </motion.h1>

      <motion.p
        className="text-2xl text-gray-500 mt-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 3, duration: 0.5 }}
      >
        Oops! You've reached a dead end
      </motion.p>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 3.5, duration: 0.5 }}
        className="mt-8"
      >
        <Link
          href="/admin/"
          className="mt-16 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Turn around and go home
        </Link>
      </motion.div>
    </div>
  );
}
