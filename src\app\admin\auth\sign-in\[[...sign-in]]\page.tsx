"use client";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import React from "react";
import { useSignIn } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import * as Clerk from "@clerk/elements/common";
import * as SignIn from "@clerk/elements/sign-in";

export default function SignInForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const { isLoaded, signIn, setActive } = useSignIn();
  const [email, setEmail] = React.useState("");
  const [password, setPassword] = React.useState("");
  const router = useRouter();

  // // Handle the submission of the sign-in form
  // const handleSubmit = async (e: React.FormEvent) => {
  //   e.preventDefault();

  //   if (!isLoaded) return;

  //   // Start the sign-in process using the email and password provided
  //   try {
  //     const signInAttempt = await signIn.create({
  //       identifier: email,
  //       password,
  //     });

  //     // If sign-in process is complete, set the created session as active
  //     // and redirect the user
  //     if (signInAttempt.status === "complete") {
  //       await setActive({ session: signInAttempt.createdSessionId });
  //       toast.success("Signed in successfully");
  //       router.push("/admin");
  //     } else {
  //       // If the status is not complete, check why. User may need to
  //       // complete further steps.
  //       console.error(JSON.stringify(signInAttempt, null, 2));
  //       toast.error(
  //         "Signed fail successfully" + JSON.stringify(signInAttempt, null, 2)
  //       );
  //     }
  //   } catch (err: any) {
  //     // See https://clerk.com/docs/custom-flows/error-handling
  //     // for more info on error handling
  //     console.error(JSON.stringify(err, null, 2));
  //     toast.error("error" + JSON.stringify(err, null, 2));
  //   }
  // };

  // Display a form to capture the user's email and password
  return (
    <>
      <div className="bg-gradient-to-br from-purple-500 to-blue-700 min-h-screen flex justify-center items-center">
        <SignIn.Root>
          <SignIn.Step
            name="start"
            className="w-full h-[80vh] space-y-6 rounded-2xl bg-black bg-opacity-70 backdrop-blur-xl justify-center items-center px-4 py-10 shadow-lg ring-1 ring-black/5 sm:w-96 sm:px-8"
          >
            <h1 className="mt-4 text-3xl text-center font-medium tracking-tight text-zinc-500">
              Sign in to your account
            </h1>

            <Clerk.GlobalError className="block text-sm text-red-400" />

            <Clerk.Field name="identifier" className="space-y-4">
              <Clerk.Label className="text-sm font-medium text-zinc-500">
                Username
              </Clerk.Label>
              <Clerk.Input className="w-full rounded-md bg-white text-zinc-900 px-3.5 py-2 text-sm outline-none ring-1 ring-inset ring-zinc-300 hover:ring-zinc-400 focus:ring-[1.5px] focus:ring-zinc-950 data-[invalid]:ring-red-400" />
              <Clerk.FieldError className="block text-sm text-red-400" />
            </Clerk.Field>

            <Clerk.Field name="password">
              <Clerk.Label className="text-sm  font-medium text-zinc-500">
                Password
              </Clerk.Label>
              <Clerk.Input className="w-full rounded-md bg-white text-zinc-900 px-3.5 py-2 text-sm outline-none ring-1 ring-inset ring-zinc-300 hover:ring-zinc-400 focus:ring-[1.5px] focus:ring-zinc-950 data-[invalid]:ring-red-400" />
              <Clerk.FieldError className="block text-sm text-red-400" />
            </Clerk.Field>

            <SignIn.Action
              submit
              className="w-full mt-4 rounded-md bg-zinc-900 px-4.5 py-3.5 text-center text-sm font-medium text-gray-300 shadow outline-none ring-1 ring-inset ring-zinc-950 hover:bg-zinc-950 focus-visible:outline-[1.5px] focus-visible:outline-offset-2 focus-visible:outline-zinc-950 active:text-white/70"
            >
              Continue
            </SignIn.Action>
          </SignIn.Step>

          <SignIn.Step name="verifications">
            <SignIn.Strategy name="email_code">
              <h1>Check your email</h1>
              <p>
                We sent a code to <SignIn.SafeIdentifier />.
              </p>

              <Clerk.Field name="code">
                <Clerk.Label>Email code</Clerk.Label>
                <Clerk.Input />
                <Clerk.FieldError className="block text-sm text-red-400" />
              </Clerk.Field>

              <SignIn.Action submit>Continue</SignIn.Action>
            </SignIn.Strategy>
          </SignIn.Step>
        </SignIn.Root>
      </div>
    </>
  );
}
