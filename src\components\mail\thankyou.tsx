interface ThankYouEmailProps {
  name: string;
  email: string;
}

export const ThankYouEmail: React.FC<ThankYouEmailProps> = ({
  name,
  email,
}) => (
  <div
    style={{
      fontFamily:
        '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
      maxWidth: "600px",
      margin: "0 auto",
      padding: "20px",
      backgroundColor: "#ffffff",
    }}
  >
    <table
      width="100%"
      cellPadding="0"
      cellSpacing="0"
      style={{ backgroundColor: "#f0f4f8", padding: "20px" }}
    >
      <tr>
        <td>
          <h1
            style={{
              color: "#2d3748",
              fontSize: "24px",
              fontWeight: "bold",
              textAlign: "center",
              margin: "0 0 20px",
            }}
          >
            Thank You for Contacting Us!
          </h1>

          <div
            style={{
              backgroundColor: "#ffffff",
              borderRadius: "8px",
              padding: "20px",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
            }}
          >
            <p
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "1.5",
                margin: "0 0 16px",
              }}
            >
              Dear {name},
            </p>

            <p
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "1.5",
                margin: "0 0 16px",
              }}
            >
              Thank you for reaching out to us. We have received your message
              and appreciate you taking the time to contact us.
            </p>

            <p
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "1.5",
                margin: "0 0 16px",
              }}
            >
              Our team will review your message and get back to you at {email}{" "}
              as soon as possible. Usually, we respond within 24-48 business
              hours.
            </p>

            <p
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "1.5",
                margin: "0 0 16px",
              }}
            >
              In the meantime, if you have any urgent concerns, please don't
              hesitate to reach out to our support team.
            </p>

            <div
              style={{
                borderTop: "1px solid #e2e8f0",
                marginTop: "20px",
                paddingTop: "20px",
              }}
            >
              <p style={{ color: "#718096", fontSize: "14px", margin: "0" }}>
                Best regards,
                <br />
                The Tech Fossil Team
              </p>
              <p style={{ marginTop: "10px" }}>
                <span color="#3b82f6">Phone app</span>
                <a href="tel:+919718791401">+91 9718791401</a>
              </p>
              <p style={{ marginTop: "10px" }}>
                <span color="#3b82f6">Whats app</span>
                <a href="whatsapp://send?phone=+919718791401?text='Hello Tech Fossil Team, I would like to know more about your Services.'">
                  +91 9718791401
                </a>
              </p>
            </div>
          </div>
        </td>
      </tr>
    </table>
  </div>
);
