import React from "react";
import ProjectCard from "./components/card";

type Props = {
  projects: any;
};

const ProjectMain = ({ projects }: Props) => {
  return (
    <div className="lg:max-w-7xl grid grid-cols-1 lg:grid-cols-2 gap-4 w-full">
      {projects.map((project: any) => (
        <div key={project.id}>
          <ProjectCard project={project} />
        </div>
      ))}
    </div>
  );
};

export default ProjectMain;
