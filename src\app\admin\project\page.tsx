import React from "react";
import DataTable from "@/components/admin/common/data-table";
import Image from "next/image";
import { convertStatusToValue } from "@/lib/utils";
import ViewButtonTable from "@/components/admin/common/view-button-table";
import PageHeader from "@/components/admin/common/page-header";
import { getProjects } from "@/actions/project";
import { projectFields } from "@/lib/staticdata";

type Props = {};

const ProjectPage = async (props: Props) => {
  const projectsData: any = [];
  const data: any = await getProjects();

  data.map((item: any) => {
    projectsData.push({
      logo: (
        <Image
          src={
            item.logo ||
            "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/preloader%2Fplaceholder.png?alt=media&token=bf1e9987-d53b-46ad-b92f-c877c3d40982"
          }
          alt={item.title}
          width={100}
          height={100}
        />
      ),
      title: item.title,
      description: item?.description?.substring(0, 250) + "...",
      slug: item.slug,
      type: item.type,
      status: item.status,
      statusText: convertStatusToValue(item.status),
      createdAt: item.createdAt,
      action: <ViewButtonTable page={"project"} slug={item.slug} />,
    });
  });

  return (
    <div className="p-8 flex flex-col justify-center items-center gap-y-8">
      <PageHeader page={"project"} fields={projectFields} />
      <DataTable data={projectsData} />
    </div>
  );
};

export default ProjectPage;
