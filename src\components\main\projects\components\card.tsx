"use client";
import React from "react";
import { <PERSON><PERSON><PERSON>, CardContainer, CardItem } from "@/components/ui/3d-card";
import Link from "next/link";
import Image from "next/image";
import { cn, convertStatusToColor, convertStatusToValue } from "@/lib/utils";

type Props = {
  project: any;
};

const ProjectCard = ({ project }: Props) => {
  project.status = project.status && parseInt(project.status);
  const color = convertStatusToColor(project.status);

  return (
    <>
      <CardContainer>
        <CardBody className="bg-gray-50 relative group/card dark:hover:shadow-2xl dark:hover:shadow-emerald-500/[0.1] dark:bg-black/70 dark:border-white/[0.2] border-black/[0.1] w-auto sm:w-[30rem] h-auto rounded-xl p-8 border">
          <CardItem
            translateZ="70"
            className="text-xl font-bold text-neutral-600 dark:text-white"
          >
            {project.title}
          </CardItem>
          <CardItem
            as="p"
            translateZ="90"
            className="text-neutral-500 text-sm max-w-sm mt-2 dark:text-neutral-300"
          >
            {project.description}
          </CardItem>
          <CardItem translateZ="120" className="w-full mt-4">
            <Image
              priority
              src={project.logo}
              height="1000"
              width="1000"
              className="h-60 backdrop-blur-xl w-full object-fit rounded-xl group-hover/card:shadow-3xl"
              alt={project.title}
            />
          </CardItem>
          <div className="flex justify-between items-center mt-20">
            <CardItem
              translateZ={80}
              as={Link}
              href={project.link}
              className="px-4 py-2 rounded-xl text-xs font-normal dark:text-white"
            >
              Try now →
            </CardItem>
            <CardItem
              translateZ={80}
              className="px-4 py-2 rounded-xl bg-black dark:bg-white dark:text-black text-white text-xs font-bold"
            >
              <Link href={`/projects/${project.slug}`}>Read More</Link>
            </CardItem>
          </div>
        </CardBody>
      </CardContainer>
    </>
  );
};

export default ProjectCard;
