import React from "react";
import { getPortfolios } from "@/actions/portfolio";
import PortfolioHero from "@/components/main/portfolio/Hero";
import PortfolioMain from "@/components/main/portfolio/Main";
import { generateMetadata1 } from "@/lib/utils";

type Props = {};

export async function generateMetadata() {
  return generateMetadata1({
    page: "portfolio",
  });
}

const Portfolio = async (props: Props) => {
  const products: any[] = [];
  const cards: any[] = [];

  const portfolios = await getPortfolios();

  portfolios.map((portfolio) => {
    products.push({
      title: portfolio.title,
      link: portfolio.link,
      thumbnail: portfolio.featureImage,
    });
  });

  portfolios.map((portfolio) => {
    cards.push({
      title: portfolio.title,
      src: portfolio.featureImage,
      link: portfolio.link,
      type: portfolio.type,
      tags: portfolio?.tags,
      slug: portfolio.slug,
    });
  });

  return (
    <div>
      <PortfolioHero products={products} />
      <PortfolioMain cards={cards} />
    </div>
  );
};

export default Portfolio;
