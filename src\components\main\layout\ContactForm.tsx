"use client";
import React, { useState } from "react";
import firebase from "../../../firebase/firebase";
import emailjs from "emailjs-com";
import { useRouter, usePathname } from "next/navigation";
import { collection, addDoc } from "firebase/firestore";
import { containsKeywords } from "../../../lib/utils";
import { createContact } from "@/actions/contact";
import toast from "react-hot-toast";

type Props = {};

const ContactForm = (props: Props) => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [message, setMessage] = useState("");
  const [success, setSuccess] = useState(false);

  const router = useRouter();
  const pathname = usePathname();
  let type = pathname;

  const createContactFirebase = async (contact: Contact) => {
    try {
      await addDoc(collection(firebase, "contact"), {
        ...contact,
        createdAt: new Date(),
      });
      console.log("Data added to Firestore successfully!");
    } catch (error) {
      console.error("Error adding data to Firestore:", error);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (containsKeywords(message)) {
      setName("");
      setEmail("");
      setPhone("");
      setMessage("");
      setSuccess(true);
      return null;
    }

    const contact = {
      name,
      email,
      phone,
      type,
      message,
    };
    createContact(contact);
    createContactFirebase(contact);

    emailjs
      .sendForm(
        "service_samep4g",
        "template_GUGGhMmd",
        e.target as HTMLFormElement,
        "user_NihGOpEhagvYwCxy105GP"
      )
      .then(
        (result) => {
          console.log(result.text);
        },
        (error) => {
          console.log(error.text);
        }
      );

    setName("");
    setEmail("");
    setPhone("");
    setMessage("");
    setSuccess(true);
    toast.success(
      "Your message is sent successfully. We will contact you soon!"
    );
  };

  return (
    <div className="relative h-full w-[40rem] p-5 bg-white rounded-md text-gray-800 shadow-[0_0_60px_5px_rgba(0,0,0,0.4)]">
      <p className="text-center text-3xl font-bold tracking-tighter leading-7">
        Contact Us
      </p>
      <p className="my-4 font-light capitalize">
        Leave Your Contact Details and We Will Get Back To You As Soon as
        Possible.
      </p>
      <form method="post" onSubmit={handleSubmit} id="contactForm">
        <input
          placeholder="Your Name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
          name="name"
          type="text"
          className="absolute bottom-[250px] w-[92%] border-b border-gray-300 p-2 bg-transparent transition-all focus:outline-none focus:border-blue-500"
        />
        <input
          placeholder="Your e-mail"
          name="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="absolute bottom-[200px] w-[92%] border-b border-gray-300 p-2 bg-transparent transition-all focus:outline-none focus:border-blue-500"
        />

        <input
          placeholder="Your Phone"
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
          name="phone"
          type="phone"
          required
          className="absolute bottom-[150px] w-[92%] border-b border-gray-300 p-2 bg-transparent transition-all focus:outline-none focus:border-blue-500"
        />

        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Your Message"
          name="message"
          required
          className="absolute bottom-[70px] w-[92%] border-b border-gray-300 p-2 bg-transparent transition-all focus:outline-none focus:border-blue-500"
        ></textarea>
        <button
          type="submit"
          className="absolute right-[-10px] bottom-[-20px] rounded-l-full rounded-tr-none bg-blue-500 text-white py-3 px-6 text-xs font-bold tracking-widest cursor-pointer transition-all shadow-[-5px_6px_20px_0px_rgba(26,26,26,0.4)] hover:bg-blue-500 hover:shadow-[-5px_6px_20px_0px_rgba(88,88,88,0.569)]"
        >
          SUBMIT
        </button>
        <div className="absolute right-[-10px] bottom-[18px] w-0 h-0 border-t-[10px] border-t-blue-500 border-l-[0px] border-r-[10px] border-r-transparent border-l-transparent"></div>
      </form>
    </div>
  );
};

export default ContactForm;
