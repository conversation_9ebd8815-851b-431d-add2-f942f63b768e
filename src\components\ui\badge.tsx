import React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { X } from "lucide-react";
import { motion, type HTMLMotionProps } from "framer-motion";

const badgeVariants = cva(
  "inline-flex items-center rounded-full px-4 py-2 text-base font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline:
          "text-foreground border border-input hover:bg-accent hover:text-accent-foreground",
        success: "bg-green-500 text-white hover:bg-green-600",
        warning: "bg-yellow-500 text-white hover:bg-yellow-600",
        info: "bg-blue-500 text-white hover:bg-blue-600",
        gradient: "bg-gradient-to-r from-pink-500 to-purple-500 text-white",
      },
      size: {
        default: "text-base",
        sm: "text-sm",
        lg: "text-lg",
      },
      shape: {
        pill: "rounded-full",
        rounded: "rounded-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      shape: "pill",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  dismissible?: boolean;
  onDismiss?: () => void;
  animation?: "bounce" | "pulse" | "shake" | "none";
}

const animations = {
  bounce: {
    y: [0, -10, 0],
    transition: {
      duration: 0.6,
      repeat: Infinity,
      repeatType: "reverse" as const,
    },
  },
  pulse: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 0.6,
      repeat: Infinity,
      repeatType: "reverse" as const,
    },
  },
  shake: {
    x: [0, -5, 5, -5, 5, 0],
    transition: {
      duration: 0.4,
      repeat: Infinity,
      repeatType: "reverse" as const,
    },
  },
  none: {},
};

export function Badge({
  className,
  variant,
  size,
  shape,
  dismissible = false,
  onDismiss,
  animation = "none",
  children,
  ...props
}: BadgeProps) {
  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{
        scale: 1,
        opacity: 1,
        ...animations[animation],
      }}
      exit={{ scale: 0.8, opacity: 0 }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className={badgeVariants({ variant, size, shape, className })}
      {...(props as HTMLMotionProps<"div">)}
    >
      {children}
      {dismissible && (
        <motion.button
          className="ml-2 rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          onClick={onDismiss}
          aria-label="Dismiss"
          whileHover={{ rotate: 90 }}
          whileTap={{ scale: 0.9 }}
        >
          <X className="h-4 w-4" />
        </motion.button>
      )}
    </motion.div>
  );
}
