import React from "react";
import DataTable from "@/components/admin/common/data-table";
import { getContacts } from "@/actions/contact";
import { Button } from "@/components/ui/button";
import Link from "next/link";

type Props = {};

const ContactPage = async (props: Props) => {
  const contacts: any = [];
  const data: any = await getContacts();

  data.map((item: any) => {
    contacts.push({
      name: item.name,
      email: item.email,
      phone: item.phone,
      message: item.message,
      type: item.type,
      status: item.status,
      createdAt: item.createdAt,
    });
  });

  return (
    <div className="p-8 flex justify-center items-center">
      <DataTable data={contacts} />
    </div>
  );
};

export default ContactPage;
