import type { Metadata } from "next";
import { Open_Sans } from "next/font/google";
import "../globals.css";
import Loading from "../loading";
import { Suspense } from "react";
import { AdminSidebar } from "@/components/admin/Layout/sidebar";
import { cn } from "@/lib/utils";
import FloatBar from "@/components/admin/Layout/float-bar";
import { ClerkProvider, UserButton } from "@clerk/nextjs";
import { auth } from "@clerk/nextjs/server";
import SignInForm from "./auth/sign-in/[[...sign-in]]/page";

const openSans = Open_Sans({
  variable: "--font-open-sans",
  subsets: ["latin"],
  weight: ["300", "400", "500", "700", "800"],
  style: ["normal", "italic"],
});

export const metadata: Metadata = {
  title: "TTF ADMIN PANEL",
};

export default async function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { userId } = await auth();
  return (
    <>
      <ClerkProvider
        publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
      >
        {userId ? (
          <Suspense fallback={<Loading />}>
            <div
              className={cn(
                "rounded-md flex flex-col md:flex-row flex-1 w-full mx-auto overflow-hidden",
                "h-screen"
              )}
            >
              <div className="block md:hidden">
                <AdminSidebar />
              </div>
              <div className="flex flex-col gap-2 flex-1 w-full h-full overflow-y-auto">
                {children}
              </div>
              <div className="hidden md:block">
                <FloatBar />
              </div>
            </div>
          </Suspense>
        ) : (
          <SignInForm />
        )}
      </ClerkProvider>
    </>
  );
}
