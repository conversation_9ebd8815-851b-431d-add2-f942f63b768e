"use server";
import api from "@/lib/api";
import { db } from "@/lib/db";
import { createPortfolioQuery, deletePortfolioQuery, getPortfolioQuery, getPortfoliosQuery, updatePortfolioQuery } from "@/lib/queries";
import { connect } from "http2";

export const createPortfolio = async (portfolio: any) => {
    const data = await createPortfolioQuery(portfolio);
    return data
}

export const getPortfolios = async () => {
    const portfolios = await getPortfoliosQuery();
    return portfolios
}

export const getPortfolio = async (slug: string) => {
    const portfolio = await getPortfolioQuery(slug);
    return portfolio
}

export const updatePortfolio = async (portfolio: any, id: any) => {
    const data = await updatePortfolioQuery(portfolio, id);
    return data
}

export const deletePortfolio = async (id: any) => {
    const data = await deletePortfolioQuery(id);
    return data
}

export const restorePortfolio = async () => {
    try {
        const data = await api.get(`/portfolio`).then((res) => res.data);
        const storedPortfolio = await Promise.all(
            await data.portfolio.map(async (portfolio: any) => {


                return await db.portfolio.create({
                    data: {
                        title: portfolio.title || "",
                        description: portfolio.description || "",
                        slug: portfolio.slug || "",
                        type: portfolio.type || "",
                        folder: portfolio.folder || "",
                        featureImage: portfolio.featureImage || "",
                        status: 1,
                        link: portfolio.link || "",
                        tags: {
                            createMany: {
                                data: portfolio.tags.split(", ").map((tag: any) => ({ text: tag }))
                            }
                        }
                    }
                });
            })
        );

        return storedPortfolio;

    } catch (err) {
        console.log(err)
        return null;
    }
}




