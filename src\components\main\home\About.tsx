"use client";

import React from "react";
import { motion } from "motion/react";
import { features } from "../../../lib/staticdata";

type Props = {};

const HomeAbout = (props: Props) => {
  return (
    <>
      <section className=" bg-white">
        <div
          className="w-full h-full lg:min-h-screen mx-auto bg-cover bg-center bg-fixed bg-no-repeat"
          style={{
            backgroundImage:
              "url('https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/homepage%2Fabout-us%2Fttf-about-us.png?alt=media&token=5eb23adb-7a8f-49e3-a509-73ec9044ff10')",
          }}
        >
          <div className="h-full pb-8 lg:pb-0 lg:min-h-screen bg-gray-800 bg-clip-padding backdrop-filter backdrop-blur-sm bg-opacity-90 backdrop-saturate-70 backdrop-contrast-100  flex items-center justify-center">
            <div className="w-full lg:w-10/12 mx-auto">
              <div className="flex flex-col lg:flex-row justify-center items-center">
                <div className="w-full lg:w-1/2 px-4 mt-8 lg:mt-0">
                  <div className="flex flex-col lg:flex-row text-center lg:text-left lg:justify-start lg:items-start">
                    <h5 className="text-sm text-blue-500 tracking-widest text-muted-foreground rotate-0 lg:-rotate-90 lg:origin-left">
                      Who We Are
                    </h5>
                    <div className="flex flex-col lg:ml-[-70px] lg:mt-[-90px]">
                      <h2 className="text-3xl font-bold text-gray-200">
                        A <strong>Story</strong>
                        <br />
                        Of
                      </h2>
                      <h2 className="text-3xl font-bold text-gray-200">
                        The Tech Fossil
                      </h2>
                    </div>
                  </div>
                </div>

                <div className="w-full lg:w-1/2 px-4 mt-8 lg:mt-0">
                  <div>
                    <h3 className="text-2xl font-semibold text-justify text-gray-200">
                      Empowering Businesses with Exceptional Website Design and
                      Development
                    </h3>
                    <p className="mt-4 text-md lg:text-lg text-gray-200 text-justify">
                      Established in 2019, The Tech Fossil is a renowned website
                      design and development company based in Delhi, India.
                      Leveraging our collective 5 years of freelancing
                      experience and collaborations with startups, we have
                      reached a level of professionalism that allows us to offer
                      our exceptional web services to a wider audience.
                    </p>
                    <p className="mt-4 text-md lg:text-lg text-gray-200 text-justify">
                      Driven by a relentless pursuit of excellence, we are
                      laser-focused on creating tailored digital solutions that
                      cater to the unique needs of our clients. We firmly
                      believe that every project must be crafted with a
                      meticulous balance of stunning design and seamless
                      functionality, ensuring an effortless user experience.
                      Constantly inspired by the valuable insights and feedback
                      from our diverse client base, we develop increasingly
                      advanced and cutting-edge projects that push the
                      boundaries of digital innovation. Whether you're an
                      aspiring entrepreneur or an established enterprise, we are
                      here to be your trusted partner in navigating the dynamic
                      digital landscape and elevating your online presence to
                      new heights.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-8 h-full lg:h-screen">
          <div className="w-full lg:w-11/12 flex flex-col lg:flex-row justify-center items-center mx-auto h-full space-x-0 lg:space-x-8">
            {features &&
              features.map((card, index) => (
                <div
                  key={index}
                  className="bg-gray-700 bg-clip-padding backdrop-filter backdrop-blur-xl bg-opacity-50 backdrop-saturate-70 backdrop-contrast-100 shadow-lg rounded-lg w-full h-full lg:w-1/3 lg:h-96 p-8 mb-8 lg:mb-0 text-center"
                >
                  <div className="about-item">
                    <div className="flex text-center justify-center items-center gap-x-4">
                      <motion.div
                        variants={card?.variants}
                        initial="normal"
                        animate="animate"
                      >
                        <card.Icon width={50} height={50} strokeWidth={2} />
                      </motion.div>
                      <h3 className="text-6xl font-semibold text-gray-200">
                        {card.title}
                      </h3>
                    </div>

                    <hr className="my-4 border-gray-300" />
                    <p className="text-gray-100">{card.description}</p>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default HomeAbout;
