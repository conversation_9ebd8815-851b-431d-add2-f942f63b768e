"use server";
import { createProjectQuery, deleteProjectQuery, getProjectQuery, getProjectsQuery, updateProjectQuery } from "@/lib/queries";

export const createProject = async (project: any) => {
    const data = await createProjectQuery(project);
    return data;
};

export const getProject = async (slug: string) => {
    const data = getProjectQuery(slug);
    return data;
};

export const getProjects = async () => {
    const project = await getProjectsQuery();
    return project;
}

export const updateProject = async (project: any, id: any) => {
    const updatedProject = await updateProjectQuery(project, id)
    return updatedProject;
}

export const deleteProject = async (id: any) => {
    const data = await deleteProjectQuery(id)
    return data
}