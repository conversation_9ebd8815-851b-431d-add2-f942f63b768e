"use client";
import React from "react";
``;
import FormModal from "./create-modal";
import CreateForm from "./create-form";
import { Edit, PlusCircle } from "lucide-react";
import toast from "react-hot-toast";
import { contentField } from "@/lib/staticdata";
import { createContent } from "@/actions/content";

type Props = {
  data?: any;
  page: string;
  id: any;
};

const CreateContentForm = ({ data, page, id }: Props) => {
  const handleSubmit = async (formData: Record<string, any>) => {
    await createContent(formData, page, id);
  };

  return (
    <>
      <FormModal
        title={!data ? `Create Content` : `Update Content`}
        buttonText={!data ? `Create Content` : `Update Content`}
        icon={
          !data ? (
            <PlusCircle size={20} className="font-bold" />
          ) : (
            <Edit size={20} className="font-bold" />
          )
        }
      >
        <CreateForm
          isUpdate={data ? true : false}
          onSubmit={handleSubmit}
          fields={contentField as any}
        />
      </FormModal>
    </>
  );
};

export default CreateContentForm;
