import { getService } from "@/actions/service";
import ServiceContent from "@/components/main/services/Content";
import ServiceHero from "@/components/main/services/Hero";
import { generateMetadata1 } from "@/lib/utils";
import React from "react";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};

export async function generateMetadata({ params }: Props) {
  const { slug } = await params;
  return generateMetadata1({
    page: slug,
  });
}

const Services = async ({ params }: Props) => {
  const { slug } = await params;

  const serviceInfo = await getService(slug);
  return (
    <>
      <div className="max-w-screen overflow-hidden">
        <ServiceHero
          slug={serviceInfo?.title}
          bgImage={serviceInfo?.featureImage as string}
        />
        <ServiceContent service={serviceInfo} />
      </div>
    </>
  );
};

export default Services;
