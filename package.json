{"name": "main-site-new-uc", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 4000", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.7.4", "@clerk/elements": "^0.22.13", "@clerk/nextjs": "^6.10.0", "@hookform/resolvers": "^3.10.0", "@lordicon/react": "^1.10.1", "@next/third-parties": "^15.1.6", "@prisma/client": "^6.2.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@react-three/drei": "^9.121.3", "@react-three/fiber": "^8.17.12", "@tabler/icons-react": "^3.29.0", "@tailwindcss/typography": "^0.5.16", "@tsparticles/engine": "^3.7.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.7.1", "@types/react-dropzone": "^5.1.0", "@types/three": "^0.172.0", "@uiw/react-md-editor": "^4.0.5", "@uploadthing/react": "^7.1.5", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^4.1.0", "emailjs-com": "^3.2.0", "embla-carousel-react": "^8.5.2", "emblor": "^1.4.7", "firebase": "^11.2.0", "framer-motion": "^12.0.1", "html-react-parser": "^5.2.2", "input-otp": "^1.4.2", "lucide-react": "^0.473.0", "marked": "^15.0.6", "md-editor-rt": "^5.2.1", "mini-svg-data-uri": "^1.4.4", "moment": "^2.30.1", "motion": "^12.0.1", "next": "15.1.6", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "ollama": "^0.5.12", "openai": "^4.80.1", "prisma": "^6.2.1", "radix-ui": "^1.0.1", "react": "^19.0.0", "react-day-picker": "9.5.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-element-to-jsx-string": "^17.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-markdown": "^9.0.3", "react-resizable-panels": "^2.1.7", "react-slugify": "^4.0.1", "recharts": "^2.15.0", "resend": "^4.1.1", "sonner": "^1.7.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.172.0", "three-globe": "^2.41.4", "uploadthing": "^7.4.4", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"typescript": "^5", "@types/node": "^22", "@types/react": "^19.0.7", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17"}}