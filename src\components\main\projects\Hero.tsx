import React from "react";
import { BackgroundLines } from "../../ui/background-lines";

type Props = {};

const ProjectHero = (props: Props) => {
  return (
    <>
      <BackgroundLines className="flex items-center justify-center w-full flex-col px-4">
        <h2 className="bg-clip-text text-transparent text-center bg-gradient-to-b from-neutral-900 to-neutral-700 dark:from-neutral-600 dark:to-white text-2xl md:text-4xl lg:text-7xl font-sans py-2 lg:py-10 relative z-20 font-bold tracking-tight">
          Our Projects
        </h2>
        <p className="max-w-4xl text-justify mx-auto text-sm lg:text-lg text-neutral-700 dark:text-neutral-400">
          Welcome to our Projects page – a showcase of innovation, creativity,
          and impact! Here, you'll discover a curated collection of our most
          remarkable endeavors, where ideas transform into reality. From
          cutting-edge technology solutions to artistic masterpieces, each
          project reflects our commitment to excellence and passion for
          progress. Dive in to explore inspiring stories, groundbreaking
          achievements, and the vision driving our success. Let our work ignite
          your imagination and fuel your own aspirations!
        </p>
      </BackgroundLines>
    </>
  );
};

export default ProjectHero;
