// 'use server';
// import Ollama from 'ollama';

// export async function generateSEOWithOllama(content: any) {
//     try {
//         console.log("chala kya?")

//         const prompt = `As an SEO expert, generate optimized metadata for this page:
//     Title: ${content.title}
//     Content: ${content.body}

//     Respond in JSON format with these fields:
//     {
//       "title": "string under 60 chars",
//       "description": "string under 160 chars",
//       "keywords": "comma-separated keywords, need like 10 to 12 keywords and also add 'the tech fossil' as one of the keyword, and return it in form of string"
//     }`;
//         const response = await Ollama.chat({
//             model: 'mistral',
//             messages: [
//                 { role: "system", content: "You are an SEO specialist with 30 years experience in technical SEO optimization." },
//                 { role: "user", content: prompt }
//             ],
//             format: 'json'
//         });

//         const rawData = response.message.content;
//         const parsedData = JSON.parse(rawData);

//         // Generate description if missing
//         if (!parsedData.description) {
//             const descPrompt = `Create an SEO description under 160 characters for:
//         Title: ${content.title}
//         Content: ${content.body.substring(0, 500)}...`;

//             const descResponse = await Ollama.chat({
//                 messages: [{ role: "user", content: descPrompt }],
//                 model: "mistral",
//                 format: 'json'
//             });

//             if (descResponse.message.content) {
//                 parsedData.description = descResponse.message.content.trim();
//             }
//         }

//         // Fallback if still missing
//         if (!parsedData.description) {
//             parsedData.description = `${content.title}. ${content.body.substring(0, 137)}...`;
//         }

//         // Validate response structure
//         if (!parsedData.title || !parsedData.description || !parsedData.keywords) {
//             throw new Error('Invalid SEO response structure');
//         }

//         // Validate and sanitize
//         return {
//             title: parsedData.title.slice(0, 60),
//             description: parsedData.description.slice(0, 160),
//             keywords: parsedData.keywords.slice(0, 1000)
//         };

//     } catch (error) {
//         console.error('Ollama generation failed:', error);
//         return {
//             title: content.slice(0, 60),
//             description: content.slice(0, 160),
//             keywords: []
//         };
//     }
// }

'use server';
import Ollama from 'ollama';

const validateDescription = (desc: string): string => {
    const MAX_LENGTH = 160;
    if (desc.length <= MAX_LENGTH && /[.!?]$/.test(desc)) return desc;

    const truncated = desc.substring(0, MAX_LENGTH);
    const lastSentenceEnd = Math.max(
        truncated.lastIndexOf('.'),
        truncated.lastIndexOf('!'),
        truncated.lastIndexOf('?')
    );

    const finalDesc = lastSentenceEnd > 0
        ? truncated.substring(0, lastSentenceEnd + 1)
        : truncated.substring(0, truncated.lastIndexOf(' ') || MAX_LENGTH);

    return finalDesc.replace(/\s([a-z])$/, (_, match) => `. ${match.toUpperCase()}`);
};

export async function generateSEOWithOllama(content: any) {
    try {
        console.log("chala?")
        const systemPrompt = `<<SYS>>
        You are an SEO expert with 30 years experience. Follow these strict rules:
        1. Descriptions must be complete sentences ending with proper punctuation
        2. Title must be 30-40 characters, Complete phrase (no truncated words) and do not include 'The Tech Fossil'
        3. Include 'the tech fossil' in keywords
        4. Keywords should be 15-20 comma-separated phrases and return as string
        5. Never truncate mid-sentence in description
        6. Never truncate mid-sentence in title
        7. Do Not Include 'The Tech Fossil' in Title
       
        FORMAT EXAMPLE:
        {
        "title": "Web Development Services",
        "description": "Professional web development services with modern frameworks. Build responsive, SEO-optimized websites.",
        "keywords": "web development, responsive design, seo optimization, modern frameworks, the tech fossil"
        }

        Examples
        GOOD Description: "Professional web design services with responsive layouts. Boost your online presence with SEO optimization."
        BAD Description: "Professional web design services with responsive layouts and SEO"
        Good Title: "Web Design Services | Custom Development"
        Bad Title: "Web Design Services | Custom Developme"
        Bad Title: "Web Design Services | Custom Developmeent - The Tech Foss"
        Bad Title: "Web Design Services | Custom Developmeent - The Tech Fossil"
        <</SYS>>`;

        const userPrompt = `Generate SEO metadata for:
        Title: ${content.title}
        Content: ${content.body}

        Respond in JSON format:
        {
        "title": "...",
        "description": "...",
        "keywords": "..."
        }`;

        const response = await Ollama.chat({
            model: 'mistral',
            messages: [
                {
                    role: "system",
                    content: systemPrompt
                },
                {
                    role: "user",
                    content: userPrompt
                }
            ],
            format: 'json',
        });

        const rawData = response.message.content;
        const parsedData = JSON.parse(rawData);

        // Validate and clean description
        let description = parsedData.description || '';
        if (!description) {
            const descResponse = await Ollama.chat({
                messages: [{
                    role: "user",
                    content: `Create complete SEO description under 160 chars for: ${content.title} - ${content.body}`
                }],
                model: "mistral",
                options: { temperature: 0.1 }
            });
            description = descResponse.message.content?.trim() || '';
        }

        // Final fallback and validation
        if (!description) {
            description = `${content.title}. ${content.body.substring(0, 137)}`;
        }

        const cleanDescription = validateDescription(description);
        const finalKeywords = parsedData.keywords
            ? `${parsedData.keywords}, the tech fossil`
                .split(',')
                .slice(0, 20)
                .join(', ')
            : 'the tech fossil';

        return {
            title: parsedData.title?.slice(0, 60) || content.title.slice(0, 60),
            description: cleanDescription.slice(0, 160),
            keywords: finalKeywords
        };

    } catch (error) {
        console.error('Ollama generation failed:', error);
        const fallbackDesc = validateDescription(`${content.title}. ${content.body}`);
        return {
            title: content.title?.slice(0, 60) || '',
            description: fallbackDesc.slice(0, 160),
            keywords: 'the tech fossil'
        };
    }
}