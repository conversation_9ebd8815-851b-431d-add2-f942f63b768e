import React from "react";
import HTMLReactParser from "html-react-parser/lib/index";
import moment from "moment";
import Image from "next/image";

import { getBlog } from "@/actions/blog";
import { generateMetadata1 } from "@/lib/utils";

type Props = {
  params: Promise<{
    slug: string;
  }>;
};

export async function generateMetadata({ params }: Props) {
  const { slug } = await params;
  return generateMetadata1({
    page: slug,
  });
}

const SingleBlogpage = async ({ params }: Props) => {
  const { slug } = await params;
  const singleBlog = await getBlog(slug);
  return (
    <>
      {singleBlog && (
        <div className="my-24 max-w-5xl mx-auto bg-black/50 shadow-3xl rounded-lg overflow-hidden">
          <div className="container px-6 py-8">
            <h2 className="mt-4 text-2xl font-bold text-gray-300">
              {singleBlog.title}
            </h2>
            <div className="flex items-center justify-between my-4">
              <span className="inline-block px-3 py-1 rounded-full text-sm font-semibold text-gray-800 bg-gray-200">
                The Tech Fossil
              </span>
              <span className="text-gray-200 text-sm">
                {moment(singleBlog.createdAt).calendar()}
              </span>
            </div>

            {singleBlog.featureImage && (
              <Image
                priority
                className="object-cover shadow-2xl rounded-lg backdrop-blur-xl backdrop-opacity-50"
                height={700}
                width={1000}
                src={singleBlog.featureImage}
                alt={singleBlog.title}
              />
            )}

            <div className="mt-4 text-gray-200">
              {HTMLReactParser(singleBlog.description)}
            </div>

            <div className="mt-8 flex items-center">
              <div className="flex-shrink-0">
                <img
                  className="h-10 w-10 rounded-full"
                  src="https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/icon%2Fglow.webp?alt=media&token=4f0dfcae-8638-4c1d-8f13-f420dc71151e"
                  alt="The Tech Fossil"
                />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-bold text-gray-300">
                  The Tech Fossil
                </h3>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SingleBlogpage;
