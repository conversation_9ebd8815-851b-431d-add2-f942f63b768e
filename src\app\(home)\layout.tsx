import type { Metada<PERSON> } from "next";
import { Open_Sans } from "next/font/google";
import "../globals.css";
import Navbar from "@/components/main/layout/Navbar";
import Footer from "@/components/main/layout/Footer";
import MoreLinks from "@/components/main/layout/MoreLinks";
import ContactUs from "@/components/main/layout/ContactUs";
import NextTopLoader from "nextjs-toploader";
import { Suspense } from "react";
import Loading from "../loading";

const openSans = Open_Sans({
  variable: "--font-open-sans",
  subsets: ["latin"],
  weight: ["300", "400", "500", "700", "800"],
  style: ["normal", "italic"],
});

export const metadata: Metadata = {
  title: {
    default: "Best Website Design Company",
    template: "[The Tech Fossil] - %s",
  },
  description:
    "Tech Fossil is a leading web design and development company serving clients in India, USA, and UK. We offer creative, responsive, and custom website solutions for businesses of all sizes.",
};

export default function MainLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <Navbar />
      <Suspense fallback={<Loading />}>
        <div className="mt-0">
          <NextTopLoader />
          {children}
        </div>
      </Suspense>
      <ContactUs />
      <MoreLinks />
      <Footer />
    </>
  );
}
