import React from "react";
import ContactForm from "./ContactForm";
import { MailIcon, PhoneOutgoingIcon } from "lucide-react";
import { IconBrandWhatsapp } from "@tabler/icons-react";
import ContactModal from "../common/contact-modal";

type Props = {};

const ContactUs = (props: Props) => {
  return (
    <section
      style={{
        backgroundImage: `url(
          "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/homepage%2Fstatements%2Fcontact-us-image.webp?alt=media&token=680d3727-01a8-4df9-912d-871b5de821da")`,
      }}
      className=" h-full lg:min-h-screen  flex justify-center items-center"
    >
      <div className="overflow-y-hidden w-full h-full lg:min-h-screen bg-black/50 backdrop-blur-sm flex justify-center items-center">
        <div className="w-full lg:max-w-7xl mx-auto p-4">
          <div className="w-full flex flex-col lg:flex-row justify-center lg:justify-between">
            {/* Left Section */}
            <div className=" bg-white/90 p-8 lg:p-12 h-full rounded-md">
              <div>
                <div className="mb-6">
                  <h5 className="text-lg font-semibold text-blue-700">
                    Who We Are
                  </h5>
                  <h2 className="text-4xl text-gray-700 font-bold leading-tight">
                    Get In <br />{" "}
                    <strong className="text-blue-600">Touch</strong>
                  </h2>
                </div>
                <div id="offices">
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-gray-800">
                      Business Information
                    </h4>
                    <ul className="mt-4 space-y-2">
                      <li className="flex items-center space-x-2">
                        <MailIcon size={20} color="#3b82f6" />
                        <a
                          href="mailto:<EMAIL>"
                          className="text-gray-900 hover:underline lowercase"
                        >
                          <EMAIL>
                        </a>
                      </li>
                      <li className="flex items-center space-x-2">
                        <PhoneOutgoingIcon size={20} color="#3b82f6" />
                        <a
                          href="tel:+919718791401"
                          className="text-gray-900 hover:underline lowercase"
                        >
                          +91 9718791401
                        </a>
                      </li>
                      <li className="flex items-center space-x-2">
                        <IconBrandWhatsapp size={20} color="#3b82f6" />
                        <a
                          href="whatsapp://send?phone=+919718791401?text='Hello Tech Fossil Team, I would like to know more about your Services.'"
                          className="text-gray-900 hover:underline lowercase"
                        >
                          +91 9718791401
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <ul className="flex space-x-4">
                  <li>
                    <a
                      href="https://facebook.com/thetechfossil"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-600 hover:text-blue-600"
                    >
                      <i className="fab fa-facebook-f"></i>
                    </a>
                  </li>
                  <li>
                    <a
                      href="https://twitter.com/thetechfossil"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-600 hover:text-blue-600"
                    >
                      <i className="fab fa-twitter"></i>
                    </a>
                  </li>
                  <li>
                    <a
                      href="https://instagram.com/thetechfossil"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-600 hover:text-blue-600"
                    >
                      <i className="fab fa-instagram"></i>
                    </a>
                  </li>
                </ul>

                <div className="lg:hidden mt-8 w-full flex justify-center items-center">
                  <ContactModal />
                </div>
              </div>
            </div>

            {/* Right Section */}
            <div className="hidden lg:block">
              <ContactForm />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactUs;
