"use client";
import React from "react";
import Image from "next/image";
import DeleteForm from "./delete-form";
import moment from "moment";
import EditForm from "./edit-form";
import NotFound from "@/app/admin/not-found";
import { deleteTag } from "@/actions/tag";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import Link from "next/link";
import ImagesUpload from "./images-upload";
import MarkDown from "@/components/common/mark-down";
import CreateContentForm from "./create-content";
import ImagePopup from "@/components/common/image-popup";
import { Badge } from "@/components/ui/badge";

type Props = {
  data: any;
  page: any;
  fields?: any;
  isContnent?: Boolean;
  isImages?: Boolean;
};

const DetailPage = ({
  data,
  page,
  fields,
  isContnent = false,
  isImages = true,
}: Props) => {
  if (!data) {
    return <NotFound />;
  }

  const router = useRouter();

  const handleDeleteTag = async (id: any) => {
    id = parseInt(id);
    const a = confirm("Are you sure you want to delete this tag?");
    if (!a) return;
    await deleteTag(id);
    toast.success("Tag Deleted Successfully!");
    router.refresh();
  };

  return (
    <>
      <div className="w-full p-6">
        <div className="flex flex-col gap-4">
          {data.featureImage && (
            <div className="rounded-lg overflow-hidden mb-6">
              <Image
                priority
                width={1000}
                height={1000}
                src={
                  data?.featureImage ||
                  "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/preloader%2Fplaceholder.png?alt=media&token=bf1e9987-d53b-46ad-b92f-c877c3d40982"
                }
                alt={data?.title}
                className="w-full h-full md:max-h-[700px] object-fit shadow-2xl "
              />
            </div>
          )}

          <div className="w-full bg-black p-6 shadow rounded-lg mb-6">
            <div className="flex gap-6 items-center mb-6">
              {data?.logo && (
                <Image
                  src={
                    data?.logo ||
                    "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/preloader%2Fplaceholder.png?alt=media&token=bf1e9987-d53b-46ad-b92f-c877c3d40982"
                  }
                  priority
                  width={250}
                  height={250}
                  alt={data?.title}
                  className="w-16 h-16 rounded-full object-cover"
                />
              )}
              <h1 className="text-4xl font-bold capitalize">{data?.title}</h1>
            </div>
            <div className="text-gray-300">
              {data?.description ? (
                <MarkDown description={data?.description} />
              ) : (
                "No description available."
              )}
            </div>
            <div className="mt-4 text-sm text-gray-200">
              {data?.slug && (
                <span>
                  <span className="mr-4">Slug:</span>
                  <code className="bg-gray-600 p-1 rounded">{data?.slug}</code>
                </span>
              )}
              {data?.page && (
                <span>
                  <span className="mr-4">Page:</span>
                  <code className="bg-gray-600 p-1 rounded">{data?.page}</code>
                </span>
              )}
              <span className="ml-4">
                Status:
                <span
                  className={
                    data?.status === 1
                      ? "text-green-700 font-bold ml-4 bg-gray-200 p-1 rounded"
                      : "text-red-700 font-bold ml-4 bg-gray-200 p-1 rounded"
                  }
                >
                  {data?.status === 1 ? "Active" : "Inactive"}
                </span>
              </span>
            </div>
            <p className="text-xs text-gray-400 mt-4">
              Created: {moment(data?.createdAt).calendar()} | Updated:{" "}
              {moment(data?.updatedAt).calendar()}
            </p>

            {data?.tags && (
              <div className="mt-4">
                <div className="flex flex-wrap gap-2">
                  {data?.tags.map((tag: any) => (
                    <Badge
                      key={tag.id}
                      variant={"secondary"}
                      dismissible
                      onDismiss={() => handleDeleteTag(tag.id)}
                    >
                      {tag.text}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            <div className="text-xs text-gray-400 mt-4 flex flex-wrap gap-4">
              <EditForm data={data} page={page} fields={fields || []} />

              <DeleteForm
                id={data?.id}
                text={data?.slug || data?.page}
                image={data?.featureImage}
                page={page}
              />

              {isImages && <ImagesUpload id={data?.id} page={page} />}

              {isContnent && <CreateContentForm page={page} id={data?.id} />}
            </div>
          </div>
        </div>
        {data?.services && (
          <>
            <h2 className="text-2xl font-semibold mb-4">
              Services Under {data?.title}
            </h2>
            {data?.services.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {data?.services.map((service: any) => (
                  <Link
                    key={service.id}
                    href={`/admin/service/${service.slug}`}
                    className="block p-4 border rounded-lg shadow hover:shadow-md"
                  >
                    <h3 className="text-lg font-medium">{service.title}</h3>
                    <p className="text-sm text-gray-600">
                      {service.description.substring(0, 250)}
                    </p>
                  </Link>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">
                No services available under this category.
              </p>
            )}
          </>
        )}

        {data?.contents && (
          <>
            <h2 className="text-2xl font-semibold mb-4">
              content Under {data?.title} {data?.contents?.length}
            </h2>
            {data?.contents.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {data?.contents.map((content: any) => (
                  <>
                    <h3 className="text-lg font-medium">{content.title}</h3>
                    <p className="text-sm text-gray-600">
                      {content.description.substring(0, 250)}
                    </p>
                  </>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">
                No content available under this category.
              </p>
            )}
          </>
        )}

        {data?.images && (
          <div className="h-full w-full p-8">
            <h2 className="text-2xl font-semibold mb-4">
              Images Under {data?.title}
            </h2>
            <ImagePopup images={data?.images} />
          </div>
        )}
      </div>
    </>
  );
};

export default DetailPage;
