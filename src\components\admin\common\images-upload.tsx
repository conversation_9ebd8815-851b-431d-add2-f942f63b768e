"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Trash, Upload } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import Modal from "./form-modal";
import { FileUpload } from "@/components/ui/file-upload";
import { useUploadThing } from "@/lib/uploadthing";
import { createImageQuery } from "@/lib/queries";
import toast from "react-hot-toast";

type Props = {
  id: any;
  page: string;
};

const ImagesUpload = ({ id, page }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadComplete, setUploadComplete] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadedLinks, setUploadedLinks] = useState<any[]>([]);

  const { startUpload } = useUploadThing("imageUploader");

  const router = useRouter();

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const handleUpload = async (files: File[]) => {
    if (!files || files.length === 0) return;

    setIsUploading(true);
    setUploadError(null);
    setUploadComplete(false);

    try {
      const uploadPromises = files.map((file) => startUpload([file]));
      const results = await Promise.all(uploadPromises);

      if (results.every((res) => res)) {
        setUploadComplete(true);
        results.map(async (res) => {
          if (res && res[0]) {
            const done = await createImageQuery(
              res[0].serverData.fileUrl,
              page,
              id
            );
            if (done) {
              isModalOpen && closeModal();
              toast.success("Images Uploaded Successfully!");
              router.refresh();
            }
          }
        });
      } else {
        throw new Error("One or more uploads failed");
      }
    } catch (error) {
      console.error("Upload error:", error);
      setUploadError("Some files failed to upload. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <>
      <Button
        className="flex justify-center items-center"
        variant={"default"}
        onClick={openModal}
      >
        <Upload width={20} height={20} />
        Upload Images
      </Button>
      <Modal isOpen={isModalOpen} onClose={closeModal} title={"Delete Parent"}>
        <FileUpload isUploading={isUploading} onChange={handleUpload} />
      </Modal>
    </>
  );
};

export default ImagesUpload;
