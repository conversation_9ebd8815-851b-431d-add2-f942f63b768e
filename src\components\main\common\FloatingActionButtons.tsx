"use client";

import { useState } from "react";
import {
  Share2,
  Phone,
  Facebook,
  Twitter,
  Instagram,
  Mail,
  Smartphone,
  MapPin,
  X,
} from "lucide-react";

const FloatingActionButtons = () => {
  const [socialOpen, setSocialOpen] = useState(false);
  const [contactOpen, setContactOpen] = useState(false);

  const toggleSocial = () => {
    setSocialOpen(!socialOpen);
    setContactOpen(false);
  };

  const toggleContact = () => {
    setContactOpen(!contactOpen);
    setSocialOpen(false);
  };

  return (
    <div className="fixed bottom-4 right-4 flex flex-row items-end space-x-4">
      {/* Social Media FAB */}
      <div className="relative">
        <button
          onClick={toggleSocial}
          className="bg-blue-500 hover:bg-blue-600 text-white rounded-full p-3 shadow-lg transition-all duration-300"
          aria-label={
            socialOpen ? "Close social media links" : "Open social media links"
          }
        >
          {socialOpen ? <X size={24} /> : <Share2 size={24} />}
        </button>
        {socialOpen && (
          <div className="absolute bottom-16 left-1/2 -translate-x-1/2 flex flex-col items-center space-y-2">
            <a
              href="https://facebook.com"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-blue-600 text-white rounded-full p-2 shadow-md hover:bg-blue-700 transition-colors duration-300"
            >
              <Facebook size={20} />
            </a>
            <a
              href="https://twitter.com"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-blue-400 text-white rounded-full p-2 shadow-md hover:bg-blue-500 transition-colors duration-300"
            >
              <Twitter size={20} />
            </a>
            <a
              href="https://instagram.com"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-pink-500 text-white rounded-full p-2 shadow-md hover:bg-pink-600 transition-colors duration-300"
            >
              <Instagram size={20} />
            </a>
          </div>
        )}
      </div>

      {/* Contact FAB */}
      <div className="relative">
        <button
          onClick={toggleContact}
          className="bg-green-500 hover:bg-green-600 text-white rounded-full p-3 shadow-lg transition-all duration-300"
          aria-label={
            contactOpen ? "Close contact links" : "Open contact links"
          }
        >
          {contactOpen ? <X size={24} /> : <Phone size={24} />}
        </button>
        {contactOpen && (
          <div className="absolute bottom-16 left-1/2 -translate-x-1/2 flex flex-col items-center space-y-2">
            <a
              href="mailto:<EMAIL>"
              className="bg-red-500 text-white rounded-full p-2 shadow-md hover:bg-red-600 transition-colors duration-300"
            >
              <Mail size={20} />
            </a>
            <a
              href="tel:+1234567890"
              className="bg-green-600 text-white rounded-full p-2 shadow-md hover:bg-green-700 transition-colors duration-300"
            >
              <Smartphone size={20} />
            </a>
            <a
              href="https://maps.google.com"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-yellow-500 text-white rounded-full p-2 shadow-md hover:bg-yellow-600 transition-colors duration-300"
            >
              <MapPin size={20} />
            </a>
          </div>
        )}
      </div>
    </div>
  );
};

export default FloatingActionButtons;
