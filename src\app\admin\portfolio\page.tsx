"use server";
import React from "react";
import { getPortfolios } from "@/actions/portfolio";
import DataTable from "@/components/admin/common/data-table";
import ViewButtonTable from "@/components/admin/common/view-button-table";
import PageHeader from "@/components/admin/common/page-header";
import { portfolioFields } from "@/lib/staticdata";
import Image from "next/image";

type Props = {};

const PortfolioPage = async (props: Props) => {
  const portfolio: any = [];

  const data: any = await getPortfolios();

  data.map((item: any) => {
    portfolio.push({
      logo: (
        <Image
          priority
          src={
            item.logo ||
            "https://firebasestorage.googleapis.com/v0/b/techfossil-96d21.appspot.com/o/preloader%2Fplaceholder.png?alt=media&token=bf1e9987-d53b-46ad-b92f-c877c3d40982"
          }
          alt={item.title}
          width={100}
          height={100}
        />
      ),
      title: item.title,
      slug: item.slug,
      type: item.type,
      status: item.status,
      createdAt: item.createdAt,
      action: <ViewButtonTable page={"portfolio"} slug={item.slug} />,
    });
  });

  return (
    <div className="p-8 flex flex-col justify-center items-center gap-y-8">
      <PageHeader page="portfolio" fields={portfolioFields} />
      <DataTable data={portfolio} page="portfolio" />
    </div>
  );
};

export default PortfolioPage;
