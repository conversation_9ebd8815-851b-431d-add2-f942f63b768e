import { OpenAI } from "openai";

const openai = new OpenAI({
    // baseURL: "https://api.deepseek.com",
    apiKey: process.env.OPENAI_API_KEY, // Store API Key in .env file
});

async function delay(ms: number | undefined) {
    return new Promise(resolve => setTimeout(resolve, ms));
}



export async function generateSEOContent(content: { title: string; body: string; }, type: string) {
    delay(1000);
    try {
        const prompt = `As an SEO expert, generate optimized metadata for this ${type}:
    Title: ${content.title}
    Content: ${content.body}
    
    Respond in JSON format with these fields:
    {
      "title": "string under 60 chars",
      "description": "string under 160 chars",
      "keywords": "comma-separated keywords"
    }`;

        const response = await openai.chat.completions.create({
            messages: [
                { role: "system", content: "You are an SEO specialist with 10 years experience in technical SEO optimization." },
                { role: "user", content: prompt }
            ],
            model: "gpt-4o-mini",
            response_format: { type: "json_object" },
        });

        const rawData = response.choices[0].message.content;
        if (!rawData) {
            throw new Error('Received null or undefined response from DeepSeek API');
        }

        const parsedData = JSON.parse(rawData);

        // Generate description if missing
        if (!parsedData.description) {
            const descPrompt = `Create an SEO description under 160 characters for:
        Title: ${content.title}
        Content: ${content.body.substring(0, 500)}...`;

            const descResponse = await openai.chat.completions.create({
                messages: [{ role: "user", content: descPrompt }],
                model: "gpt-4o-mini",
            });

            if (descResponse.choices[0].message.content) {
                parsedData.description = descResponse.choices[0].message.content.trim();
            }
        }

        // Fallback if still missing
        if (!parsedData.description) {
            parsedData.description = `${content.title}. ${content.body.substring(0, 137)}...`;
        }


        // Validate response structure
        if (!parsedData.title || !parsedData.description || !parsedData.keywords) {
            throw new Error('Invalid SEO response structure');
        }

        return {
            title: parsedData.title.slice(0, 60),
            description: parsedData.description.slice(0, 160),
            keywords: parsedData.keywords.replace(/, /g, ',').slice(0, 255)
        };

    } catch (error) {
        console.error('DeepSeek API Error:', error);
        if (error instanceof Error) {
            throw new Error('SEO generation failed: ' + error.message);
        } else {
            throw new Error('SEO generation failed: ' + String(error));
        }
    }
}