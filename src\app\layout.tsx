import type { <PERSON>ada<PERSON> } from "next";
import { Open_Sans } from "next/font/google";
import "./globals.css";
import { Toaster } from "react-hot-toast";
import NextTopLoader from "nextjs-toploader";
import { Suspense } from "react";
import Loading from "./loading";
import { GoogleAnalytics, GoogleTagManager } from "@next/third-parties/google";

const openSans = Open_Sans({
  variable: "--font-open-sans",
  subsets: ["latin"],
  weight: ["300", "400", "500", "700", "800"],
  style: ["normal", "italic"],
});

export const metadata: Metadata = {
  title: {
    default: "Best Website Design Company",
    template: "[The Tech Fossil] - %s",
  },
  description:
    "Tech Fossil is a leading web design and development company serving clients in India, USA, and UK. We offer creative, responsive, and custom website solutions for businesses of all sizes.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${openSans.className} antialiased dark bg-black bg-opacity-90`}
      >
        <Suspense fallback={<Loading />}>
          <div className="mt-0">
            <NextTopLoader />
            {children}
          </div>
        </Suspense>

        <Toaster position="bottom-right" />
      </body>
      <GoogleAnalytics gaId="G-HF3HNP6JXL" />
      <GoogleTagManager gtmId="G-HF3HNP6JXL" />
    </html>
  );
}
